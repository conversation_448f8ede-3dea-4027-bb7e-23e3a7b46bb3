<?php
/* Copyright (C) 2009       <PERSON>        <<EMAIL>>
 * Copyright (C) 2010-2016  <PERSON><PERSON>	       <<EMAIL>>
 * Copyright (C) 2013-2018  <PERSON>             <<EMAIL>>
 * Copyright (C) 2015       <PERSON><PERSON><PERSON>         <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */


/**
 *      \file       htdocs/admin/traitereceipts.php
 *		\ingroup    bank
 *		\brief      Page to setup the traite module
 */

// Load Dolibarr environment
require '../main.inc.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/admin.lib.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/pdf.lib.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/bank.lib.php';
require_once DOL_DOCUMENT_ROOT.'/compta/paiement/traite/class/bordereautraite.class.php';

// Load translation files required by the page
$langs->loadLangs(array("admin", "companies", "bills", "other", "banks"));

if (!$user->admin) {
    accessforbidden();
}

$action = GETPOST('action', 'aZ09');
$value = GETPOST('value', 'alpha');

if (empty($conf->global->TRAITERECEIPTS_ADDON)) {
    $conf->global->TRAITERECEIPTS_ADDON = 'mod_traitereceipts_mint.php';
}

if (empty($conf->global->TRAITERECEIPTS_THYME_MASK)) {
    dolibarr_set_const($db, "TRAITERECEIPTS_THYME_MASK", "BT{yy}{mm}-{0000}", 'chaine', 0, '', $conf->entity);
}

/*
 * Actions
 */
if ($action == 'updateMask') {
    $maskconst = GETPOST('maskconsttraitereceipts', 'aZ09');
    $mask = GETPOST('masktraitereceipts', 'alpha');
    if ($maskconst && preg_match('/_MASK$/', $maskconst)) {
        $res = dolibarr_set_const($db, $maskconst, $mask, 'chaine', 0, '', $conf->entity);
    }
    if (!($res > 0)) {
        $error++;
    }
    if (!$error) {
        setEventMessages($langs->trans("SetupSaved"), null, 'mesgs');
    } else {
        setEventMessages($langs->trans("Error"), null, 'errors');
    }
}

if ($action == 'setmod') {
    dolibarr_set_const($db, "TRAITERECEIPTS_ADDON", $value, 'chaine', 0, '', $conf->entity);
}

if ($action == 'set_BANK_TRAITERECEIPT_FREE_TEXT') {
    $freetext = GETPOST('BANK_TRAITERECEIPT_FREE_TEXT', 'restricthtml');
    $res = dolibarr_set_const($db, "BANK_TRAITERECEIPT_FREE_TEXT", $freetext, 'chaine', 0, '', $conf->entity);
    if (!($res > 0)) {
        $error++;
    }
    if (!$error) {
        setEventMessages($langs->trans("SetupSaved"), null, 'mesgs');
    } else {
        setEventMessages($langs->trans("Error"), null, 'errors');
    }
}

/*
 * View
 */

$dirmodels = array_merge(array('/'), (array) $conf->modules_parts['models']);
llxHeader("", $langs->trans("TraiteSetupModule"));

$form = new Form($db);

$linkback = '<a href="'.DOL_URL_ROOT.'/admin/modules.php?restore_lastsearch_values=1">'.$langs->trans("BackToModuleList").'</a>';
print load_fiche_titre($langs->trans("TraiteSetupModule"), $linkback, 'title_setup');

$head = bank_admin_prepare_head(null);
print dol_get_fiche_head($head, 'traitereceipts', $langs->trans("TraiteSetupModule"), -1, 'account');

/*
 *  Numbering module
 */

print load_fiche_titre($langs->trans("TraiteReceiptsNumberingModule"), '', '');

print '<table class="noborder centpercent">';
print '<tr class="liste_titre">';
print '<td>'.$langs->trans("Name").'</td>';
print '<td>'.$langs->trans("Description").'</td>';
print '<td class="nowrap">'.$langs->trans("Example").'</td>';
print '<td class="center" width="60">'.$langs->trans("Status").'</td>';
print '<td class="center" width="16">'.$langs->trans("ShortInfo").'</td>';
print '</tr>'."\n";

clearstatcache();

foreach ($dirmodels as $reldir) {
    // Scan directory core/modules/traite
    $dir = dol_buildpath($reldir."core/modules/traite/");

    if (!is_dir($dir)) {
        continue;
    }

    $handle = opendir($dir);
    if (is_resource($handle)) {
        while (($file = readdir($handle)) !== false) {
            if (!is_dir($dir.$file) || (substr($file, 0, 1) <> '.' && substr($file, 0, 3) <> 'CVS')) {
                if (preg_match('/mod_traitereceipts_/', $file) && substr($file, dol_strlen($file) - 3, 3) == 'php') {
                    $file = substr($file, 0, dol_strlen($file) - 4);

                    require_once $dir.$file.'.php';

                    $module = new $file($db);

                    // Show modules according to features level
                    if ($module->version == 'development' && $conf->global->MAIN_FEATURES_LEVEL < 2) continue;
                    if ($module->version == 'experimental' && $conf->global->MAIN_FEATURES_LEVEL < 1) continue;

                    if ($module->isEnabled()) {
                        print '<tr class="oddeven">';
                        print '<td>';
                        // Modification de la récupération du nom
                        // Au lieu d'utiliser directement $module->name qui peut ne pas être une chaîne
                        $moduleName = '';
                        if (!empty($module->name) && is_string($module->name)) {
                            $moduleName = $langs->trans($module->name);
                        } else {
                            $moduleName = $langs->trans(ucfirst(str_replace('mod_traitereceipts_', '', $file)));
                        }
                        print $moduleName;
                        print "</td>\n";
                        print '<td>\n';
                        print $module->info();
                        print '</td>';

                        // Show example of numbering model
                        print '<td class="nowrap">';
                        $tmp = $module->getExample();
                        if (preg_match('/^Error/', $tmp)) print '<div class="error">'.$langs->trans($tmp).'</div>';
                        elseif ($tmp == 'NotConfigured') print $langs->trans($tmp);
                        else print $tmp;
                        print '</td>'."\n";

                        print '<td class="center">';
                        if ($conf->global->TRAITERECEIPTS_ADDON == $file) {
                            print img_picto($langs->trans("Activated"), 'switch_on');
                        } else {
                            print '<a class="reposition" href="'.$_SERVER["PHP_SELF"].'?action=setmod&token='.newToken().'&value='.$file.'">';
                            print img_picto($langs->trans("Disabled"), 'switch_off');
                            print '</a>';
                        }
                        print '</td>';

                        // Example
                        $htmltooltip = '';
                        $htmltooltip.= ''.$langs->trans("Version").': <b>'.$module->getVersion().'</b><br>';
                        $nextval = $module->getNextValue($mysoc, null);
                        if ("$nextval" != $langs->trans("NotAvailable")) {
                            $htmltooltip.= $langs->trans("NextValue").': ';
                            if ($nextval) {
                                $htmltooltip.= $nextval.'<br>';
                            } else {
                                $htmltooltip.= $langs->trans($module->error).'<br>';
                            }
                        }

                        print '<td class="center">';
                        print $form->textwithpicto('', $htmltooltip, 1, 0);
                        print '</td>';

                        print "</tr>\n";
                    }
                }
            }
        }
        closedir($handle);
    }
}

print '</table><br>';


/*
 * Other options
 */
print load_fiche_titre($langs->trans("OtherOptions"), '', '');

print '<form action="'.$_SERVER["PHP_SELF"].'" method="post">';
print '<input type="hidden" name="token" value="'.newToken().'">';
print '<input type="hidden" name="action" value="set_BANK_TRAITERECEIPT_FREE_TEXT">';

print '<table class="noborder centpercent">';
print '<tr class="liste_titre">';
print '<td>'.$langs->trans("Parameters").'</td>';
print '<td class="center" width="60">&nbsp;</td>';
print '<td width="80">&nbsp;</td>';
print "</tr>\n";

$substitutionarray = pdf_getSubstitutionArray($langs, null, null, 2);
$substitutionarray['__(AnyTranslationKey)__'] = $langs->trans("Translation");
$htmltext = '<i>'.$langs->trans("AvailableVariables").':<br>';
foreach ($substitutionarray as $key => $val) {
    $htmltext .= $key.'<br>';
}
$htmltext .= '</i>';

print '<tr class="oddeven"><td colspan="2">';
print $form->textwithpicto($langs->trans("FreeLegalTextOnTraiteReceipts"), $langs->trans("AddCRIfTooLong").'<br><br>'.$htmltext, 1, 'help', '', 0, 2, 'freetexttooltip').'<br>';
$variablename = 'BANK_TRAITERECEIPT_FREE_TEXT';
if (empty($conf->global->PDF_ALLOW_HTML_FOR_FREE_TEXT)) {
    print '<textarea name="'.$variablename.'" class="flat" cols="120">'.getDolGlobalString($variablename).'</textarea>';
} else {
    include_once DOL_DOCUMENT_ROOT.'/core/class/doleditor.class.php';
    $doleditor = new DolEditor($variablename, getDolGlobalString($variablename), '', 80, 'dolibarr_notes');
    print $doleditor->Create();
}
print '</td><td class="right">';
print '<input type="submit" class="button button-edit" value="'.$langs->trans("Modify").'">';
print "</td></tr>\n";
print '</table>';

print dol_get_fiche_end();

print '</form>';

llxFooter();
$db->close();
print '<td class="center" width="60">&nbsp;</td>';
print '<td width="80">&nbsp;</td>';
print "</tr>\n";

$substitutionarray = pdf_getSubstitutionArray($langs, null, null, 2);
$substitutionarray['__(AnyTranslationKey)__'] = $langs->trans("Translation");
$htmltext = '<i>'.$langs->trans("AvailableVariables").':<br>';
foreach ($substitutionarray as $key => $val) {
    $htmltext .= $key.'<br>';
}
$htmltext .= '</i>';

print '<tr class="oddeven"><td colspan="2">';
print $form->textwithpicto($langs->trans("FreeLegalTextOnTraiteReceipts"), $langs->trans("AddCRIfTooLong").'<br><br>'.$htmltext, 1, 'help', '', 0, 2, 'freetexttooltip').'<br>';
$variablename = 'BANK_TRAITERECEIPT_FREE_TEXT';
if (empty($conf->global->PDF_ALLOW_HTML_FOR_FREE_TEXT)) {
    print '<textarea name="'.$variablename.'" class="flat" cols="120">'.getDolGlobalString($variablename).'</textarea>';
} else {
    include_once DOL_DOCUMENT_ROOT.'/core/class/doleditor.class.php';
    $doleditor = new DolEditor($variablename, getDolGlobalString($variablename), '', 80, 'dolibarr_notes');
    print $doleditor->Create();
}
print '</td><td class="right">';
print '<input type="submit" class="button button-edit" value="'.$langs->trans("Modify").'">';
print "</td></tr>\n";
print '</table>';

print dol_get_fiche_end();

print '</form>';

llxFooter();
$db->close();
