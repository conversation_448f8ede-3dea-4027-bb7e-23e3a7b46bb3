<?php

require '../main.inc.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/admin.lib.php';
require_once DOL_DOCUMENT_ROOT.'/modulebi/lib/modulebi.lib.php';

global $langs, $user;

$langs->load("modulebi@modulebi");

// Security check
if (!$user->admin) accessforbidden();

$action = GETPOST('action', 'alpha');

if (preg_match('/set_(.*)/', $action, $reg))
{
    $code=$reg[1];
    $value=GETPOST($code);
    if (dolibarr_set_const($db, $code, $value, 'chaine', 0, '', $conf->entity) > 0)
    {
        header("Location: ".$_SERVER["PHP_SELF"]);
        exit;
    }
}

llxHeader('', $langs->trans("ModulebiSetup"));

$linkback='<a href="'.DOL_URL_ROOT.'/admin/modules.php">'.$langs->trans("BackToModuleList").'</a>';
print load_fiche_titre($langs->trans("ModulebiSetup"), $linkback, 'title_setup');

dol_fiche_end();

llxFooter();
$db->close();