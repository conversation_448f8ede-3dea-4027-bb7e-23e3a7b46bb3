<?php
/* Copyright (C) 2010-2012	<PERSON>		<<EMAIL>>
 * Copyright (C) 2011		<PERSON>	<<EMAIL>>
 * Copyright (C) 2012-2018  <PERSON>      <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

/**
 *	\file       htdocs/adherents/canvas/default/actions_adherentcard_default.class.php
 *	\ingroup    member
 *	\brief      File of class Thirdparty member card controller (default canvas)
 */
include_once DOL_DOCUMENT_ROOT.'/adherents/canvas/actions_adherentcard_common.class.php';

/**
 *	\class      ActionsAdherentCardDefault
 *	\brief      Class allowing the management of the members by default
 */
class ActionsAdherentCardDefault extends ActionsAdherentCardCommon
{
	/**
	 *	Constructor
	 *
	 *	@param	DoliDB	$db				Handler acces data base
	 *	@param	string	$dirmodule		Name of directory of module
	 *	@param	string	$targetmodule	Name of directory of module where canvas is stored
	 *	@param	string	$canvas			Name of canvas
	 *	@param	string	$card			Name of tab (sub-canvas)
	 */
	public function __construct($db, $dirmodule, $targetmodule, $canvas, $card)
	{
		$this->db               = $db;
		$this->dirmodule = $dirmodule;
		$this->targetmodule     = $targetmodule;
		$this->canvas           = $canvas;
		$this->card             = $card;
	}

	/**
	 * 	Return the title of card
	 *
	 * 	@param	string	$action		Action code
	 * 	@return	string				Title
	 */
	private function getTitle($action)
	{
		global $langs, $conf;

		$out = '';

		if ($action == 'view') {
			$out .= (!empty($conf->global->ADHERENT_ADDRESSES_MANAGEMENT) ? $langs->trans("Adherent") : $langs->trans("ContactAddress"));
		}
		if ($action == 'edit') {
			$out .= (!empty($conf->global->ADHERENT_ADDRESSES_MANAGEMENT) ? $langs->trans("EditAdherent") : $langs->trans("EditAdherentAddress"));
		}
		if ($action == 'create') {
			$out .= (!empty($conf->global->ADHERENT_ADDRESSES_MANAGEMENT) ? $langs->trans("NewAdherent") : $langs->trans("NewAdherentAddress"));
		}

		return $out;
	}

	// phpcs:disable PEAR.NamingConventions.ValidFunctionName.ScopeNotCamelCaps
	/**
	 *  Assign custom values for canvas
	 *
	 *  @param	string		$action    	Type of action
	 *  @param	int			$id				Id
	 *  @return	void
	 */
	public function assign_values(&$action, $id)
	{
		// phpcs:enable
		global $conf, $db, $langs, $user;
		global $form;

		$ret = $this->getObject($id);

		parent::assign_values($action, $id);

		$this->tpl['title'] = $this->getTitle($action);
		$this->tpl['error'] = $this->error;
		$this->tpl['errors'] = $this->errors;

		if ($action == 'view') {
			// Card header
			$head = member_prepare_head($this->object);
			$title = $this->getTitle($action);

			$this->tpl['showhead'] = dol_get_fiche_head($head, 'card', $title, 0, 'adherent');
			$this->tpl['showend'] = dol_get_fiche_end();

			$objsoc = new Societe($db);
			$objsoc->fetch($this->object->socid);

			$this->tpl['actionstodo'] = show_actions_todo($conf, $langs, $db, $objsoc, $this->object, 1);

			$this->tpl['actionsdone'] = show_actions_done($conf, $langs, $db, $objsoc, $this->object, 1);
		} else {
			// Confirm delete contact
			if ($action == 'delete' && $user->hasRight('adherent', 'supprimer')) {
				$this->tpl['action_delete'] = $form->formconfirm($_SERVER["PHP_SELF"]."?id=".$this->object->id, $langs->trans("DeleteAdherent"), $langs->trans("ConfirmDeleteAdherent"), "confirm_delete", '', 0, 1);
			}
		}
	}
}
