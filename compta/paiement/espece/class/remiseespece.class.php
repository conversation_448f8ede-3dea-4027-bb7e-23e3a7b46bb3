<?php
/* Copyright (C) 2006      <PERSON><PERSON><PERSON> <<EMAIL>>
 * Copyright (C) 2007-2011 <PERSON>  <<EMAIL>>
 * Copyright (C) 2005-2009 <PERSON>        <<EMAIL>>
 * Copyright (C) 2011-2016 <PERSON><PERSON>        <<EMAIL>>
 * Copyright (C) 2015      <PERSON>        <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

/**
 *	\file       sinedtyi/compta/paiement/espece/class/remiseespece.class.php
 *	\ingroup    compta
 *	\brief      File with class to manage cash delivery receipts
 */
require_once DOL_DOCUMENT_ROOT.'/core/class/commonobject.class.php';
require_once DOL_DOCUMENT_ROOT.'/compta/facture/class/facture.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/modules/espece/doc/pdf_blochetesp.class.php';


/**
 *	Class to manage espece delivery receipts
 */
class RemiseEspece extends CommonObject
{
    public $element = 'espece';
    public $table_element = 'espece_receipt';
    public $lines = array();
    
    public $amount;
    public $date_creation;
    public $date_validation;
    public $ref;
    public $statut;
    public $user_validation;
    public $fk_bank_account;
    
    /**
     *  Constructor
     */
    public function __construct($db)
    {
        $this->db = $db;
    }

    /**
     * Validate data before insertion
     * @return boolean true if valid, false if not
     */
    private function validate()
    {
        $errors = array();

        if (empty($this->fk_bank_account)) {
            $errors[] = 'BankAccountRequired';
        }
        if (empty($this->amount) || !is_numeric($this->amount)) {
            $errors[] = 'InvalidAmount';
        }
        if (!isset($this->date_bordereau) || $this->date_bordereau == '') {
            $errors[] = 'DateRequired';
        }
        
        // Log validation
        $logfile = DOL_DATA_ROOT.'/dolibarr.log';
        $this->writeLog($logfile, "Validation errors check: ".(!empty($errors) ? implode(', ', $errors) : 'no errors'));
        
        if (!empty($errors)) {
            $this->error = implode(', ', $errors);
            return false;
        }
        
        return true;
    }

    /**
     * Create cash deposit in database
     * @param User $user User making creation
     * @return int <0 if KO, Id of created object if OK
     */
    public function create($user)
    {
        global $conf;
        
        dol_syslog(__METHOD__ . " start", LOG_DEBUG);
        
        $now = dol_now();
        
        $sql = "INSERT INTO ".MAIN_DB_PREFIX."bordereau_cash (";
        $sql.= " ref,";
        $sql.= " datec,";
        $sql.= " date_bordereau,";
        $sql.= " fk_user_author,";
        $sql.= " fk_bank_account,";
        $sql.= " amount,";
        $sql.= " nbcash,";
        $sql.= " statut,";
        $sql.= " entity";
        $sql.= ") VALUES (";
        $sql.= " '".$this->db->escape($this->ref)."',";
        $sql.= " '".$this->db->idate($now)."',";
        $sql.= " '".$this->db->idate($this->date_bordereau)."',";
        $sql.= " ".$user->id.",";
        $sql.= " ".(int) $this->fk_bank_account.",";
        $sql.= " ".(float) price2num($this->amount).",";
        $sql.= " ".(int) $this->nbcash.",";
        $sql.= " 0,";  // Draft status
        $sql.= " ".$conf->entity;
        $sql.= ")";

        dol_syslog(__METHOD__ . " sql=" . $sql, LOG_DEBUG);
        
        $resql = $this->db->query($sql);
        if ($resql) {
            $this->id = $this->db->last_insert_id(MAIN_DB_PREFIX."bordereau_cash");
            dol_syslog(__METHOD__ . " success - id=" . $this->id, LOG_DEBUG);
            return $this->id;
        } else {
            $this->error = $this->db->lasterror();
            dol_syslog(__METHOD__ . " error=" . $this->error, LOG_ERR);
            return -1;
        }
    }

    

    /**
     * Charge le compte bancaire associé
     */
    public function loadBankAccount()
    {
        global $db;
        
        if ($this->fk_bank_account > 0) {
            require_once DOL_DOCUMENT_ROOT.'/compta/bank/class/account.class.php';
            $this->bank_account = new Account($db);
            $result = $this->bank_account->fetch($this->fk_bank_account);
            if ($result > 0) {
                $this->account_id = $this->bank_account->id;
                return 1;
            }
        }
        return -1;
    }

    /**
     * Charge les informations du bordereau
     */
    public function fetch($id, $ref = '')
    {
        global $conf;
        
        $sql = "SELECT b.rowid, b.ref, b.datec, b.date_bordereau,";
        $sql.= " b.fk_user_author, b.fk_bank_account, b.statut,";
        $sql.= " b.amount, b.nbcash, b.note";
        $sql.= " FROM ".MAIN_DB_PREFIX."bordereau_cash as b";
        $sql.= " WHERE b.entity = ".$conf->entity;
        if ($id) {
            $sql.= " AND b.rowid = ".(int) $id;
        } elseif ($ref) {
            $sql.= " AND b.ref = '".$this->db->escape($ref)."'";
        }

        $resql = $this->db->query($sql);
        if ($resql) {
            if ($this->db->num_rows($resql)) {
                $obj = $this->db->fetch_object($resql);

                $this->id = $obj->rowid;
                $this->ref = $obj->ref;
                $this->datec = $this->db->jdate($obj->datec);
                $this->date_bordereau = $this->db->jdate($obj->date_bordereau);
                $this->fk_user_author = $obj->fk_user_author;
                $this->fk_bank_account = $obj->fk_bank_account;
                $this->statut = $obj->statut;
                $this->amount = $obj->amount;
                $this->nbcash = $obj->nbcash;
                $this->note = $obj->note;

                // Load bank account
                if ($this->fk_bank_account > 0) {
                    require_once DOL_DOCUMENT_ROOT.'/compta/bank/class/account.class.php';
                    $this->bank_account = new Account($this->db);
                    $this->bank_account->fetch($this->fk_bank_account);
                }

                $this->db->free($resql);
                return 1;
            }
            $this->db->free($resql);
            return 0;
        }
        $this->error = $this->db->lasterror();
        return -1;
    }

    /**
     * Load object in memory from the database
     *
     * @param  int     $id   Id object
     * @return int          <0 if KO, 0 if not found, >0 if OK
     */
   

    /**
     * Get next reference number for cash deposit
     *
     * @return string Reference number or false if error
     */
    public function getNextNumRef()
    {
        global $conf;
        
        dol_syslog("RemiseEspece::getNextNumRef", LOG_DEBUG);

        // Get numbering model name
        $model = !empty($conf->global->ESPECERECEIPTS_ADDON) ? $conf->global->ESPECERECEIPTS_ADDON : 'mod_bordereau_cash_mint';
        
        // Get year for prefix
        $year = substr(dol_print_date(dol_now(), '%Y'), 2, 2);
        
        // Define prefix based on model
        switch ($model) {
            case 'mod_bordereau_cash_thyme':
                $prefix = 'TE'.$year.'-';
                break;
            case 'mod_bordereau_cash_mint':
                $prefix = 'ME'.$year.'-';
                break;
            case 'mod_bordereau_cash_simple':
                $prefix = 'BE'.$year.'-';
                break;
            default:
                $prefix = 'ME'.$year.'-';
        }

        // Get last number used
        $sql = "SELECT MAX(CAST(SUBSTRING(ref, ".(strlen($prefix)+1).") AS SIGNED)) as max_number";
        $sql.= " FROM ".MAIN_DB_PREFIX."bordereau_cash";
        $sql.= " WHERE ref LIKE '".$this->db->escape($prefix)."%'";
        $sql.= " AND entity = ".$conf->entity;

        dol_syslog("getNextNumRef sql=".$sql, LOG_DEBUG);
        
        $resql = $this->db->query($sql);
        if ($resql) {
            $obj = $this->db->fetch_object($resql);
            $max = intval($obj->max_number);
            $nextval = $max + 1;
            
            // Format number with leading zeros (5 digits)
            $nextref = $prefix.sprintf('%05d', $nextval);
            
            // Verify final length <= 30 chars
            if (strlen($nextref) > 30) {
                dol_syslog("Error: Reference too long ".$nextref, LOG_ERR);
                $this->error = 'ErrorRefTooLong';
                return false;
            }
            
            dol_syslog("getNextNumRef return ".$nextref, LOG_DEBUG);
            return $nextref;
        }

        $this->error = $this->db->lasterror();
        dol_syslog("getNextNumRef error ".$this->error, LOG_ERR);
        return false;
    }

    /**
     * Helper method to write to log file
     */
    private function writeLog($logfile, $message)
    {
        $timestamp = date('Y-m-d H:i:s');
        file_put_contents($logfile, "$timestamp - $message\n", FILE_APPEND);
    }

    /**
     * Return label of status
     *
     * @param   int     $mode   0=long label, 1=short label, 2=Picto + short label, 3=Picto, 4=Picto + long label, 5=Short label + Picto, 6=Long label + Picto
     * @return  string          Label of status
     */
    public function LibStatut($status, $mode = 0)
    {
        global $langs;

        // Init/load array of translation of status
        if (empty($this->labelStatus) || empty($this->labelStatusShort)) {
            global $langs;
            $langs->load("banks");
            $this->labelStatus[0] = $langs->transnoentitiesnoconv('Draft');
            $this->labelStatus[1] = $langs->transnoentitiesnoconv('Validated');
            $this->labelStatusShort[0] = $langs->transnoentitiesnoconv('Draft');
            $this->labelStatusShort[1] = $langs->transnoentitiesnoconv('Validated');
        }

        if ($mode == 0) {
            return $this->labelStatus[$status];
        } elseif ($mode == 1) {
            return $this->labelStatusShort[$status];
        } elseif ($mode == 2) {
            if ($status == 0) return img_picto($this->labelStatusShort[$status], 'statut0').' '.$this->labelStatusShort[$status];
            if ($status == 1) return img_picto($this->labelStatusShort[$status], 'statut4').' '.$this->labelStatusShort[$status];
        } elseif ($mode == 3) {
            if ($status == 0) return img_picto($this->labelStatusShort[$status], 'statut0');
            if ($status == 1) return img_picto($this->labelStatusShort[$status], 'statut4');
        } elseif ($mode == 4) {
            if ($status == 0) return img_picto($this->labelStatusShort[$status], 'statut0').' '.$this->labelStatus[$status];
            if ($status == 1) return img_picto($this->labelStatusShort[$status], 'statut4').' '.$this->labelStatus[$status];
        } elseif ($mode == 5) {
            if ($status == 0) return $this->labelStatusShort[$status].' '.img_picto($this->labelStatusShort[$status], 'statut0');
            if ($status == 1) return $this->labelStatusShort[$status].' '.img_picto($this->labelStatusShort[$status], 'statut4');
        } elseif ($mode == 6) {
            if ($status == 0) return $this->labelStatus[$status].' '.img_picto($this->labelStatusShort[$status], 'statut0');
            if ($status == 1) return $this->labelStatus[$status].' '.img_picto($this->labelStatusShort[$status], 'statut4');
        }
        return '';
    }

    /**
     * Update record
     *
     * @param  User    $user       User making update
     * @return int                 <0 if KO, >0 if OK
     */
    public function update($user)
    {
        global $conf, $langs;

        $this->db->begin();

        $sql = "UPDATE ".MAIN_DB_PREFIX."bordereau_cash";
        $sql.= " SET statut = ".(int) $this->statut;
        $sql.= " WHERE rowid = ".(int) $this->id;

        dol_syslog(get_class($this)."::update", LOG_DEBUG);
        $resql = $this->db->query($sql);
        if ($resql) {
            $this->db->commit();
            return 1;
        } else {
            $this->error = $this->db->lasterror();
            $this->db->rollback();
            return -1;
        }
    }

    /**
     * Delete cash deposit
     *
     * @param User $user User that deletes
     * @return int <0 if KO, >0 if OK
     */
    public function delete($user)
    {
        $this->db->begin();

        // Reset bank lines
        $sql = "UPDATE ".MAIN_DB_PREFIX."bank SET fk_bordereau = NULL WHERE fk_bordereau = ".(int) $this->id;
        $resql = $this->db->query($sql);

        if ($resql) {
            // Delete bordereau
            $sql = "DELETE FROM ".MAIN_DB_PREFIX."bordereau_cash WHERE rowid = ".(int) $this->id;
            $resql = $this->db->query($sql);
            
            if ($resql) {
                $this->db->commit();
                return 1;
            }
        }
        
        $this->error = $this->db->lasterror();
        $this->db->rollback();
        return -1;
    }

    /**
     * Generate PDF for cash deposit receipt
     *
     * @param string    $modele      Force template to use ('' by default)
     * @param Translate $outputlangs Object lang to use for translation
     * @return int                   <0 if KO, >0 if OK
     */
    public function generatePdf($modele = '', $outputlangs = '')
    {
        global $conf, $langs;

        if (empty($outputlangs)) $outputlangs = $langs;

        // Define output directory
        $outputdir = $conf->bank->dir_output.'/espece';
        if (! file_exists($outputdir)) {
            if (dol_mkdir($outputdir) < 0) {
                $this->error = $langs->trans("ErrorCanNotCreateDir", $outputdir);
                return -1;
            }
        }

        if (empty($modele)) {
            $modele = 'blochetesp';
        }

        dol_syslog("RemiseEspece::generatePdf model=".$modele." outputdir=".$outputdir);

        // Initialize PDF class
        $classname = "pdf_".$modele;
        $obj = new $classname($this->db);

        // Generate PDF
        $result = $obj->write_file($this, $outputdir, $this->ref, $outputlangs);
        if ($result <= 0) {
            $this->error = $obj->error;
            $this->errors = $obj->errors;
            dol_syslog("RemiseEspece::generatePdf Error: ".$this->error, LOG_ERR);
            return -1;
        }

        return 1;
    }

    /**
     * Load payment lines into $this->lines - Not needed for cash deposits
     * as we only use bordereau_cash table
     */
    public function getLinesArray()
    {
        // We don't need lines for cash deposits
        // Everything is in bordereau_cash table
        $this->lines = array();
        return 1;
    }

    /**
     * Method to fetch bank account info
     *
     * @return  int     <0 if KO, >0 if OK
     */
    public function fetch_bank_account()
    {
        global $db;

        if ($this->fk_bank_account > 0) {
            require_once DOL_DOCUMENT_ROOT.'/compta/bank/class/account.class.php';
            $this->account = new Account($db);
            return $this->account->fetch($this->fk_bank_account);
        }
        return 0;
    }

}
?>