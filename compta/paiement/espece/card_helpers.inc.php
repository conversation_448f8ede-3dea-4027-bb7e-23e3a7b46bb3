<?php
/**
 * Display functions for cash deposit slip card
 */

function getJavaScriptCalculations()
{
    return '<script type="text/javascript">
        function updateTotals() {
            var total = 0;
            var count = 0;
            var checkboxes = document.querySelectorAll(\'input[name="toRemise[]"]:checked\');
            
            checkboxes.forEach(function(checkbox) {
                count++;
                var row = checkbox.closest(\'tr\');
                var amount = parseFloat(row.querySelector(\'.amount\').dataset.amount);
                if (!isNaN(amount)) {
                    total += amount;
                }
            });
            
            document.getElementById(\'amount\').value = total.toFixed(2);
            document.getElementById(\'nbcash\').value = count;
        }
    </script>';
}

function displayBankAccountField($form)
{
    global $langs;
    
    print '<tr><td class="fieldrequired">'.$langs->trans('BankAccount').'</td><td>';
    print $form->select_comptes('', 'accountid', 0, '', 1);
    print '</td></tr>';
}

// Add other display functions...