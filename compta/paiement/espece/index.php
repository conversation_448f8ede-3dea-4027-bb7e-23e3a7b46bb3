<?php
/* Copyright (C) 2006      <PERSON><PERSON><PERSON> <<EMAIL>>
 * Copyright (C) 2007-2011 <PERSON>  <<EMAIL>>
 * Copyright (C) 2009      <PERSON>        <<EMAIL>>
 * Copyright (C) 2016      <PERSON><PERSON>	    <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

/**
 *	\file		sinedtyi/compta/paiement/espece/card.php
 *	\ingroup	bank, invoice
 *	\brief		Page for cash deposits
 */

// Load Dolibarr environment
require '../../../main.inc.php';
require_once DOL_DOCUMENT_ROOT.'/compta/paiement/espece/class/remiseespece.class.php';
require_once DOL_DOCUMENT_ROOT.'/compta/bank/class/account.class.php';

// Load translation files required by the page
$langs->loadLangs(array('banks', 'categories', 'compta', 'bills'));

$checkdepositstatic = new RemiseEspece($db);
$accountstatic = new Account($db);

// Security check
if ($user->socid) {
	$socid = $user->socid;
}
$result = restrictedArea($user, 'banque', '', '');

$usercancreate = $user->hasRight('banque', 'espece');


/*
 * Actions
 */

// None


/*
 * View
 */

llxHeader('', $langs->trans("CashReceiptsArea"));

$newcardbutton = '';
if ($user->rights->banque->cheque) {
    $newcardbutton = dolGetButtonTitle($langs->trans('NewCashDeposit'), '', 'fa fa-plus-circle', DOL_URL_ROOT.'/compta/paiement/espece/card.php?action=new');
}

print '<div class="fichecenter">';

// Left Section - Half page width
print '<div class="fichethirdleft">';

print load_fiche_titre($langs->trans("CashReceiptsArea"), $newcardbutton, 'bank_account');

// Première requête - nombre de remises en attente
$sql = "SELECT count(DISTINCT b.rowid) as nb";
$sql .= " FROM ".MAIN_DB_PREFIX."bank as b";
$sql .= " LEFT JOIN ".MAIN_DB_PREFIX."bank_url as bu ON bu.fk_bank = b.rowid";
$sql .= " LEFT JOIN ".MAIN_DB_PREFIX."bank_account as ba ON ba.rowid = b.fk_account";
$sql .= " WHERE ba.rowid = b.fk_account";
$sql .= " AND b.fk_type = 'LIQ'";  // Type espèces
$sql .= " AND b.fk_bordereau = 0";
$sql .= " AND b.amount > 0";
$sql .= " AND ba.courant = 2";  // Ajout du filtre pour la caisse
$sql .= " AND bu.type = 'payment'"; // Uniquement les paiements de factures clients

$resql = $db->query($sql);

print '<div class="div-table-responsive-no-min">';
print '<table class="noborder centpercent">';
print '<tr class="liste_titre">';
print '<th colspan="2">'.$langs->trans("CashDeposits")."</th>\n";
print "</tr>\n";

if ($resql) {
	$num = '';
	if ($obj = $db->fetch_object($resql)) {
		$num = $obj->nb;
	}
	print '<tr class="oddeven">';
	print '<td>'.$langs->trans("CashDepositsToReceipt").'</td>';
	print '<td class="right">';
	print '<a class="badge badge-info" href="'.DOL_URL_ROOT.'/compta/paiement/espece/card.php?leftmenu=customers_bills_checks&action=new">'.$num.'</a>';
	print '</td></tr>';
} else {
	dol_print_error($db);
}
print "</table></div>\n";


print '</div>';

// Right Section - Half page width
print '<div class="fichehalfright">';
print load_fiche_titre($langs->trans("LastCashReceipts"), '', '');

$max = 10;

$sql = "SELECT DISTINCT bc.rowid, bc.date_bordereau as db, bc.amount, bc.ref, bc.statut,";
$sql.= " bc.nbcash,";
$sql.= " ba.ref as bref, ba.label, ba.rowid as bid";
$sql.= " FROM ".MAIN_DB_PREFIX."bordereau_cash as bc";
$sql.= " LEFT JOIN ".MAIN_DB_PREFIX."bank_account as ba ON ba.rowid = bc.fk_bank_account";
$sql.= " WHERE bc.entity = " . $conf->entity;
$sql.= " ORDER BY bc.date_bordereau DESC, bc.rowid DESC";
$sql.= " LIMIT " . ((int) $max);

$resql = $db->query($sql);

print '<div class="div-table-responsive-no-min">';
print '<table class="noborder centpercent">';
print '<tr class="liste_titre">';
print '<th>'.$langs->trans("Ref").'</th>';
print '<th>'.$langs->trans("Date").'</th>';
print '<th>'.$langs->trans("Account").'</th>';
print '<th class="right">'.$langs->trans("NbOfCashLines").'</th>';
print '<th class="right">'.$langs->trans("Amount").'</th>';
print '<th class="right">'.$langs->trans("Status").'</th>';
print '</tr>';

if ($resql && $db->num_rows($resql) > 0) {
    while ($obj = $db->fetch_object($resql)) {
        print '<tr class="oddeven">';
        // Reference
        print '<td class="nowraponall">';
        print '<a href="card.php?id='.$obj->rowid.'">'.$obj->ref.'</a>';
        print '</td>';
        // Date
        print '<td>'.dol_print_date($db->jdate($obj->db), 'day').'</td>';
        // Account
        print '<td>'.$obj->bref.'</td>';
        // Number of cash lines
        print '<td class="right">'.$obj->nbcash.'</td>';
        // Amount
        print '<td class="right"><span class="amount">'.price($obj->amount).'</span></td>';
        // Status
        print '<td class="right">';
        print $checkdepositstatic->LibStatut($obj->statut, 3);
        print '</td>';
        print '</tr>';
    }
} else {
    print '<tr><td colspan="6" class="opacitymedium">'.$langs->trans("NoRecordFound").'</td></tr>';
}

print '</table>';
print '</div>';

$db->free($resql);
print '</div>';

print '</div>'; // End fichecenter

// Modifie la requête SQL pour récupérer toutes les factures payées en espèces
$sql = "SELECT DISTINCT";
$sql.= " p.rowid, p.datep as dp, p.amount as pamount,";
$sql.= " f.rowid as facid, f.ref as facref, f.total_ttc,";
$sql.= " s.nom as socname, s.rowid as socid,";
$sql.= " b.fk_bordereau, b.amount,";
$sql.= " ba.rowid as bid, ba.ref as bref, ba.label as blabel";
$sql.= " FROM ".MAIN_DB_PREFIX."facture as f";
$sql.= " INNER JOIN ".MAIN_DB_PREFIX."paiement_facture as pf ON pf.fk_facture = f.rowid";
$sql.= " INNER JOIN ".MAIN_DB_PREFIX."paiement as p ON p.rowid = pf.fk_paiement";
$sql.= " INNER JOIN ".MAIN_DB_PREFIX."bank as b ON b.rowid = p.fk_bank";
$sql.= " INNER JOIN ".MAIN_DB_PREFIX."bank_account as ba ON ba.rowid = b.fk_account";
$sql.= " LEFT JOIN ".MAIN_DB_PREFIX."societe as s ON s.rowid = f.fk_soc";
$sql.= " WHERE f.entity IN (".getEntity('invoice').")";
$sql.= " AND f.paye = 1"; // Factures payées
$sql.= " AND f.fk_mode_reglement = 4"; // Mode règlement espèces
$sql.= " AND b.fk_type = 'LIQ'"; // Type espèces dans bank
$sql.= " AND ba.courant = 2"; // Compte de type caisse
$sql.= " AND ba.entity IN (".getEntity('bank_account').")";
$sql.= " AND b.amount > 0";

if ($search_ref) {
    $sql .= natural_search("p.ref", $search_ref);
}
if ($search_account > 0) {
    $sql .= " AND b.fk_account = ".((int) $search_account);
}
if ($search_amount) {
    $sql .= natural_search("b.amount", $search_amount, 1);
}
if ($search_company) {
    $sql .= natural_search("s.nom", $search_company);
}

// End of page
llxFooter();
$db->close();
?>
