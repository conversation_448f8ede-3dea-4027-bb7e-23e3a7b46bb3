<?php
// Add error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load Dolibarr environment
require '../../../main.inc.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/html.form.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/bank.lib.php';
require_once './class/remiseespece.class.php';
require_once './lib/card.lib.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/files.lib.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/pdf.lib.php';
require_once DOL_DOCUMENT_ROOT.'/core/modules/espece/modules_especereceipts.php';

// Initialize technical objects
$object = new RemiseEspece($db);
$form = new Form($db);

// Get parameters
$action = GETPOST('action', 'aZ09');
$id = GETPOST('id', 'int');

// Define permissions - place this BEFORE security check
$permissiontoread = $user->rights->sinedtyi->espece->read;
$permissiontodelete = $user->rights->sinedtyi->espece->delete;
$permissiontocreate = $user->rights->sinedtyi->espece->write;

if ($user->admin) {
    $permissiontoread = 1;
    $permissiontodelete = 1;
    $permissiontocreate = 1;
}

// Security check - use proper permission
if (!$permissiontoread) {
    accessforbidden();
}

// Load object
if ($id > 0) {
    $result = $object->fetch($id);
    if ($result < 0) {
        dol_print_error($db, $object->error);
        exit;
    }
}

// Permissions
$permissiontodelete = $user->rights->sinedtyi->espece->delete;

/*
 * Actions
 */
if ($action == 'create') {
    dol_syslog("=== START CREATE CASH DEPOSIT ===", LOG_DEBUG);
    
    $error = 0;
    
    // Get form data with debug logging
    $accountid = GETPOST('accountid', 'int');
    $amount = price2num(GETPOST('amount', 'alpha'));  // Changed from default price2num
    $nbcash = GETPOST('nbcash', 'int');
    $selectedLines = GETPOST('toRemise', 'array');
    
    dol_syslog("Form data received:", LOG_DEBUG);
    dol_syslog("accountid: $accountid", LOG_DEBUG);
    dol_syslog("amount: $amount", LOG_DEBUG);
    dol_syslog("nbcash: $nbcash", LOG_DEBUG);
    dol_syslog("selectedLines: " . print_r($selectedLines, true), LOG_DEBUG);
    
    // Validate required fields
    if (empty($accountid)) {
        setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentities("BankAccount")), null, 'errors');
        $error++;
    }
    if (empty($selectedLines)) {
        setEventMessages($langs->trans("ErrorFieldRequired", $langs->trans("SelectLines")), null, 'errors');
        $error++;
    }

    if (!$error) {
        // Calculate amount and nbcash from selected lines if not provided
        if (empty($amount) || empty($nbcash)) {
            $amount = 0;
            $nbcash = count($selectedLines);
            
            if (!empty($selectedLines)) {
                $sql = "SELECT SUM(amount) as total FROM ".MAIN_DB_PREFIX."bank";
                $sql.= " WHERE rowid IN (".implode(',', array_map('intval', $selectedLines)).")";
                $resql = $db->query($sql);
                if ($resql) {
                    $obj = $db->fetch_object($resql);
                    $amount = $obj->total;
                }
            }
        }
        
        $object = new RemiseEspece($db);
        $object->ref = $object->getNextNumRef();
        $object->fk_bank_account = $accountid;
        $object->amount = $amount;
        $object->nbcash = $nbcash;
        $object->date_bordereau = dol_now();
        
        dol_syslog("Creating deposit with values:", LOG_DEBUG);
        dol_syslog("ref: ".$object->ref, LOG_DEBUG);
        dol_syslog("amount: ".$object->amount, LOG_DEBUG);
        dol_syslog("nbcash: ".$object->nbcash, LOG_DEBUG);
        
        $db->begin();
        
        $result = $object->create($user);
        if ($result > 0) {
            // Update bank lines
            $sql = "UPDATE ".MAIN_DB_PREFIX."bank SET fk_bordereau = ".$result;
            $sql.= " WHERE rowid IN (".implode(',', array_map('intval', $selectedLines)).")";
            
            if ($db->query($sql)) {
                $db->commit();
                // Redirect with success message
                setEventMessages($langs->trans("CashDepositCreated"), null, 'mesgs');
                header("Location: ".$_SERVER['PHP_SELF'].'?id='.$result);
                exit;
            } else {
                $db->rollback();
                setEventMessages($db->lasterror(), null, 'errors');
            }
        } else {
            $db->rollback();
            setEventMessages($object->error, $object->errors, 'errors');
        }
    }
    $action = 'new';  // Stay on create page on error
} elseif ($action == 'validate' && $permissiontocreate) {
    $object->fetch($id);
    if ($object->statut == 0) {
        $object->statut = 1;
        $result = $object->update($user);
        if ($result > 0) {
            setEventMessages($langs->trans("CashDepositValidated"), null, 'mesgs');
            header('Location: '.$_SERVER["PHP_SELF"].'?id='.$object->id);
            exit;
        }
    }
} elseif ($action == 'delete' && $permissiontodelete) {
    $object->fetch($id);
    $result = $object->delete($user);
    if ($result > 0) {
        setEventMessages($langs->trans("CashDepositDeleted"), null, 'mesgs');
        header('Location: list.php');
        exit;
    }
} elseif ($action == 'builddoc' && $permissiontocreate) {
    $object->fetch($id);
    $object->fetch_thirdparty();
    
    // Get bank account info if available
    if ($object->fk_bank_account > 0) {
        $object->fetch_bank_account();
    }
    
    // Important: Load the lines before PDF generation
    $object->getLinesArray();
    
    $outputlangs = $langs;
    $newlang = GETPOST('lang_id', 'aZ09');
    if (getDolGlobalInt('MAIN_MULTILANGS') && empty($newlang)) {
        $newlang = $langs->getDefaultLang();
    }
    if (!empty($newlang)) {
        $outputlangs = new Translate("", $conf);
        $outputlangs->setDefaultLang($newlang);
    }

    // Generate PDF document
    $result = $object->generatePdf('blochetesp', $outputlangs);
    
    if ($result <= 0) {
        setEventMessages($object->error, $object->errors, 'errors');
    } else {
        // Get PDF file path
        $filedir = $conf->bank->dir_output.'/espece';
        $file = $filedir.'/'.$object->ref.'.pdf';
        
        if (file_exists($file)) {
            // Force download
            header('Content-Type: application/pdf');
            header('Content-Disposition: attachment; filename="'.$object->ref.'.pdf"');
            header('Content-Length: ' . filesize($file));
            readfile($file);
            exit;
        } else {
            setEventMessages($langs->trans('ErrorPDFNotFound'), null, 'errors');
        }
    }
    
    $action = '';
} elseif ($action == 'confirm_validate' && $confirm == 'yes' && $permissiontocreate) {
    $object->fetch($id);
    if ($object->fk_bank_account > 0) {
        $object->fetch_bank_account();
    }
    
    $result = $object->validate($user);
    if ($result >= 0) {
        // Message will be set by validate() method
        header('Location: '.$_SERVER["PHP_SELF"].'?id='.$object->id);
        exit;
    } else {
        setEventMessages($object->error, $object->errors, 'errors');
    }
}

/*
 * View
 */
$title = $langs->trans("CashDepositSlip");
llxHeader('', $title);

if ($action == 'new') {
    dol_syslog("=== START NEW CASH DEPOSIT FORM ===", LOG_DEBUG);
    
    print load_fiche_titre($langs->trans("NewCashDepositSlip"));
    
    // Add Return to list button at top right after title
    print '<div class="tabBar">';
    print '<div class="inline-block floatright">';
    print '<a href="list.php" class="butAction">'.$langs->trans("BackToList").'</a>';
    print '<br>';
    // Add Status display below return button
    if ($object->status == 0) {
        print '<div class="center" style="margin-top: 10px;">';
        print '<span class="badge badge-status0 badge-status" title="'.$langs->trans("Draft").'">';
        print $langs->trans("Draft");
        print '</span>';
        print '</div>';
    }
    print '</div>';
    print '</div>';

    print '<form method="POST" action="'.$_SERVER["PHP_SELF"].'">';
    print '<input type="hidden" name="token" value="'.newToken().'">';
    print '<input type="hidden" name="action" value="create">';
    
    print '<table class="border centpercent tableforfield">';
    
    // Bank account - modified to show only standard bank accounts
    print '<tr>';
    print '<td class="fieldrequired">'.$langs->trans("BankAccount").'</td>';
    print '<td>';
    // Get only standard bank accounts (courant=1)
    $sql = "SELECT rowid, ref, label, courant, rappro";
    $sql.= " FROM ".MAIN_DB_PREFIX."bank_account";
    $sql.= " WHERE entity IN (".getEntity('bank_account').")";
    $sql.= " AND courant = 1"; // Only standard bank accounts
    $sql.= " AND clos = 0";    // Only active accounts
    $sql.= " ORDER BY label";
    
    $resql = $db->query($sql);
    if ($resql) {
        print '<select name="accountid" class="flat minwidth200" required>';
        print '<option value="">&nbsp;</option>';
        while ($obj = $db->fetch_object($resql)) {
            print '<option value="'.$obj->rowid.'">';
            print $obj->label;
            print '</option>';
        }
        print '</select>';
    }
    print '</td>';
    print '</tr>';

    // Date
    print '<tr>';
    print '<td class="fieldrequired">'.$langs->trans("Date").'</td>';
    print '<td>';
    print $form->selectDate(dol_now(), 'datebordereau', 0, 0, 0, '', 1, 1);
    print '</td>';
    print '</tr>';
    
    print '</table>';
    
    // Summary section
    print '<div id="summary_section" style="display:none;">';
    print '<table class="border centpercent">';
    print '<tr class="liste_total">';
    print '<td class="fieldrequired">'.$langs->trans("Amount").'</td>';
    print '<td><input type="text" name="amount" id="amount" readonly></td>';
    print '</tr>';
    print '<tr class="liste_total">';
    print '<td>'.$langs->trans("NumberOfLines").'</td>';
    print '<td><input type="text" name="nbcash" id="nbcash" readonly></td>';
    print '</tr>';
    print '</table>';
    print '</div>';
    
    // Payment lines table
    print_payments_table($db);
    
    // Buttons
    print '<div class="center">';
    print '<input type="submit" class="button" value="'.$langs->trans("Create").'">';
    print '</div>';
    
    print '</form>';
} elseif ($id > 0) {
    // Show cash deposit card
    $head = bordereaux_prepare_head($object);
    print dol_get_fiche_head($head, 'card', $langs->trans("CashDepositSlip"), -1, 'payment');

    // Get prev/next refs
    $sql = "SELECT MAX(rowid) as prev FROM ".MAIN_DB_PREFIX."bordereau_cash WHERE rowid < ".(int) $object->id;
    $resql = $db->query($sql);
    $prev_id = $db->fetch_object($resql)->prev;
    
    $sql = "SELECT MIN(rowid) as next FROM ".MAIN_DB_PREFIX."bordereau_cash WHERE rowid > ".(int) $object->id;
    $resql = $db->query($sql);
    $next_id = $db->fetch_object($resql)->next;

    // Add Return to list button and navigation arrows at the top
    print '<div class="tabBar">';
    print '<div class="inline-block floatright">';
    
    // Previous arrow
    if ($prev_id) {
        print '<a class="butAction" href="'.$_SERVER["PHP_SELF"].'?id='.$prev_id.'">';
        print img_previous($langs->trans("Previous"));
        print '</a> ';
    }
    
    print '<a href="list.php" class="butAction">'.$langs->trans("BackToList").'</a>';
    
    // Next arrow
    if ($next_id) {
        print ' <a class="butAction" href="'.$_SERVER["PHP_SELF"].'?id='.$next_id.'">';
        print img_next($langs->trans("Next"));
        print '</a>';
    }

    print '<br>';
    print '<div class="center" style="margin-top: 10px;">';
    if ($object->statut == 0) {  // Changed from status to statut
        print '<span class="badge badge-status0 badge-status" title="'.$langs->trans("Draft").'">';
        print $langs->trans("Draft");
        print '</span>';
    } else {
        print '<span class="badge badge-status4 badge-status" title="'.$langs->trans("Validated").'">';
        print $langs->trans("Validated");
        print '</span>';
    }
    print '</div>';
    print '</div>';
    print '</div>';

    // Information table
    print '<div class="fichecenter">';
    print '<table class="border tableforfield centpercent">';
    
    // Reference
    print '<tr><td class="titlefield width30">'.$langs->trans("Ref").'</td>';
    print '<td>'.$object->ref.'</td></tr>';

    // Date
    print '<tr><td>'.$langs->trans("Date").'</td>';
    print '<td>'.dol_print_date($object->date_bordereau, '%d/%m/%Y').'</td></tr>';

    // Account to credit
    print '<tr><td>'.$langs->trans("AccountToCredit").'</td>';
    print '<td>';
    $accountstatic = new Account($db);
    if (!empty($object->fk_bank_account)) {
        $accountstatic->fetch($object->fk_bank_account);
        print $accountstatic->label;
    }
    print '</td></tr>';

    // Amount
    print '<tr><td>'.$langs->trans("Amount").'</td>';
    print '<td>'.price($object->amount, 0, '', 1, -1, -1, '').'</td></tr>';

    // Number of lines
    print '<tr><td>'.$langs->trans("NumberOfLines").'</td>';
    print '<td>'.$object->nbcash.'</td></tr>';

    print '</table>';
    print '</div>';

    // Bank lines table
    print '<br>';
    print '<table class="noborder centpercent">';
    print '<tr class="liste_titre">';
    print '<td width="5%">'.$langs->trans("Line").'</td>';
    print '<td width="15%">'.$langs->trans("Date").'</td>';
    print '<td>'.$langs->trans("Description").'</td>';
    print '<td class="right" width="15%">'.$langs->trans("Amount").'</td>';
    print '</tr>';

    $sql = "SELECT b.rowid, b.dateo, b.label, b.amount";
    $sql.= " FROM ".MAIN_DB_PREFIX."bank as b";
    $sql.= " WHERE b.fk_bordereau = ".(int) $object->id;
    $sql.= " ORDER BY b.dateo ASC";

    $resql = $db->query($sql);
    if ($resql) {
        $i = 1;
        while ($obj = $db->fetch_object($resql)) {
            print '<tr class="oddeven">';
            print '<td>'.$i.'</td>';
            print '<td>'.dol_print_date($db->jdate($obj->dateo), '%d/%m/%Y').'</td>';
            print '<td>'.$obj->label.'</td>';
            print '<td class="right nowrap amount">'.price($obj->amount, 0, '', 1, -1, -1, '').'</td>';
            print '</tr>';
            $i++;
        }
    }
    print '</table>';

    // Actions buttons - SIMPLIFIED VERSION
    print '<div class="tabsAction">';
    // Validate button for draft status
    if ($object->statut == 0) {
        print '<a class="butAction" href="'.$_SERVER["PHP_SELF"].'?id='.$object->id.'&action=validate">';
        print $langs->trans("Validate");
        print '</a>';
    }
    // Delete button always available
    print '<a class="butActionDelete" href="'.$_SERVER["PHP_SELF"].'?id='.$object->id.'&action=delete">';
    print $langs->trans("Delete");
    print '</a>';
    print '</div>';

    // PDF generation button for validated deposit
    if ($object->statut == 1) {
        print '<div class="tabsAction">';
        print '<a class="butAction" href="'.$_SERVER["PHP_SELF"].'?id='.$object->id.'&action=builddoc">';
        print $langs->trans('GeneratePDF');
        print '</a>';
        print '</div>';
    }

    print dol_get_fiche_end();
}

llxFooter();
$db->close();
?>