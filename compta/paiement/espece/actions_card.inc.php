<?php
// Add error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
if ($action == 'create') {
    $error = 0;
    dol_syslog("=== START CREATE CASH DEPOSIT ===", LOG_DEBUG);

    // Get form data
    $accountid = GETPOST('accountid', 'int');
    $amount = price2num(GETPOST('amount'));
    $nbcash = GETPOST('nbcash', 'int');
    $selectedLines = GETPOST('toRemise', 'array');
    
    dol_syslog("Form data: accountid=$accountid, amount=$amount, nbcash=$nbcash", LOG_DEBUG);
    dol_syslog("Selected lines: " . print_r($selectedLines, true), LOG_DEBUG);

    if (empty($accountid) || empty($selectedLines) || empty($amount)) {
        $error++;
        setEventMessages($langs->trans("ErrorFieldRequired"), null, 'errors');
    }

    if (!$error) {
        $object = new RemiseEspece($db);
        
        // Set values
        $object->ref = $object->getNextNumRef();  // Get next free ref
        $object->fk_bank_account = $accountid;
        $object->amount = $amount;
        $object->nbcash = $nbcash;
        $object->date_bordereau = dol_now();
        $object->fk_user_author = $user->id;
        $object->statut = 0;  // Draft

        dol_syslog("Creating deposit with ref=" . $object->ref, LOG_DEBUG);
        
        $db->begin();
        $result = $object->create($user);
        
        if ($result > 0) {
            // Update bank lines
            $sql = "UPDATE ".MAIN_DB_PREFIX."bank";
            $sql.= " SET fk_bordereau = " . $result;
            $sql.= " WHERE rowid IN (" . implode(',', array_map('intval', $selectedLines)) . ")";
            
            dol_syslog("Update bank lines: " . $sql, LOG_DEBUG);
            
            if ($db->query($sql)) {
                $db->commit();
                setEventMessages($langs->trans("CashDepositCreated"), null);
                header("Location: " . $_SERVER["PHP_SELF"] . "?id=" . $result);
                exit;
            } else {
                $db->rollback();
                $error++;
                dol_syslog("Error updating bank lines: " . $db->lasterror(), LOG_ERR);
            }
        } else {
            $db->rollback();
            $error++;
            dol_syslog("Error creating deposit: " . $object->error, LOG_ERR);
        }
    }
}