CREATE TABLE llx_bordereau_cash (
    rowid integer AUTO_INCREMENT PRIMARY KEY,
    datec datetime,                        -- date creation
    date_bordereau datetime,               -- date of cash deposit
    fk_user_author integer NOT NULL,       -- user creating record  
    fk_bank_account integer NOT NULL,      -- bank account
    amount double(24,8) DEFAULT 0,         -- total amount
    ref varchar(30) NOT NULL,              -- reference 
    statut smallint DEFAULT 0,             -- status
    ref_ext varchar(255),                  -- external reference
    note text,                             -- notes
    nbcash integer,                        -- number of cash deposits
    entity integer DEFAULT 1 NOT NULL       -- multi company id
) ENGINE=innodb;
