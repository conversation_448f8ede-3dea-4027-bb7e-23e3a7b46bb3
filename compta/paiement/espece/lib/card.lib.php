<?php

// Add error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

/**
 * Library for cash deposit slip card
 */

function print_create_form($object, $form, $db)
{
    global $langs;
    
    // Add JavaScript for calculations
    print get_js_calculations();
    
    print '<form method="POST" action="'.$_SERVER["PHP_SELF"].'">';
    print '<input type="hidden" name="token" value="'.newToken().'">';
    print '<input type="hidden" name="action" value="create">';
    
    // Form sections
    print_form_header($form);
    print_payments_table($db);
    
    print '<div class="center">';
    print '<input type="submit" class="button" value="'.$langs->trans('Create').'">';
    print '</div>';
    
    print '</form>';
}

function get_js_calculations()
{
    return '<script type="text/javascript">
        function updateTotals() {
            var total = 0;
            var count = 0;
            var checkboxes = document.querySelectorAll(\'input[name="toRemise[]"]:checked\');
            
            checkboxes.forEach(function(checkbox) {
                count++;
                var row = checkbox.closest(\'tr\');
                var amount = parseFloat(row.querySelector(\'.amount\').dataset.amount);
                if (!isNaN(amount)) {
                    total += amount;
                }
            });
            
            document.getElementById(\'amount\').value = total.toFixed(2);
            document.getElementById(\'nbcash\').value = count;
        }
    </script>';
}

/**
 * Validate form data
 */
function validate_cash_deposit_form($object)
{
    global $langs;
    $errors = array();
    
    // Bank account validation
    if (empty($object->fk_bank_account)) {
        $errors[] = $langs->trans('ErrorFieldRequired', $langs->transnoentities('BankAccount'));
    }

    // Amount validation
    if (empty($object->amount) || $object->amount <= 0) {
        $errors[] = $langs->trans('ErrorFieldRequired', $langs->transnoentities('Amount'));
    }

    // Date validation
    if (empty($object->date_bordereau)) {
        $errors[] = $langs->trans('ErrorFieldRequired', $langs->transnoentities('Date'));
    }

    // Selected lines validation
    if (empty($object->nbcash) || $object->nbcash <= 0) {
        $errors[] = $langs->trans('ErrorNoLinesSelected');
    }

    return $errors;
}

/**
 * Print form header section with improved validation
 */
function print_form_header($form)
{
    global $langs;
    
    print '<table class="border centpercent">';
    
    // Bank account with required field marker
    print '<tr><td class="fieldrequired">'.$langs->trans('BankAccount').'</td><td>';
    print $form->select_comptes('', 'accountid', 0, '', 1, '', 0, 'minwidth200');
    print '</td></tr>';
    
    // Amount and number of lines (readonly, calculated by JS)
    print '<tr>';
    print '<td class="fieldrequired">'.$langs->trans('Amount').'</td>';
    print '<td><input name="amount" id="amount" size="10" readonly required>';
    print ' <span class="opacitymedium">'.$langs->trans('CalculatedFromSelectedLines').'</span>';
    print '</td></tr>';
    
    print '<tr>';
    print '<td class="fieldrequired">'.$langs->trans('NumberOfLines').'</td>';
    print '<td><input name="nbcash" id="nbcash" size="5" readonly required></td>';
    print '</tr>';
    
    // Date with required field marker
    print '<tr><td class="fieldrequired">'.$langs->trans('Date').'</td><td>';
    print $form->selectDate(dol_now(), 'datebordereau', 0, 0, 0, '', 1, 1);
    print '</td></tr>';
    
    print '</table>';
}

/**
 * Print payments table from bank entries
 *
 * @param DoliDB $db Database handler
 * @return void
 */
function print_payments_table($db)
{
    global $langs;
    
    // Add JavaScript for calculations
    print '<script type="text/javascript">
        function updateTotals() {
            var total = 0;
            var count = 0;
            var checkboxes = document.getElementsByName("toRemise[]");
            
            for(var i=0; i<checkboxes.length; i++) {
                if(checkboxes[i].checked) {
                    count++;
                    var amount = parseFloat(checkboxes[i].getAttribute("data-amount"));
                    if (!isNaN(amount)) {
                        total += amount;
                    }
                }
            }
            
            document.getElementById("amount").value = total.toFixed(2);
            document.getElementById("nbcash").value = count;
            
            // Show/hide summary section
            var summarySection = document.getElementById("summary_section");
            summarySection.style.display = (count > 0) ? "table-row" : "none";
        }
        
        function toggleAll(source) {
            var checkboxes = document.getElementsByName("toRemise[]");
            for(var i=0; i<checkboxes.length; i++) {
                checkboxes[i].checked = source.checked;
            }
            updateTotals();
        }
    </script>';

    // Add totals display section above the table
    print '<div class="tabsAction">';
    print '<table class="border centpercent" id="totalsSection" style="display:none;">';
    print '<tr>';
    print '<td class="titlefield">'.$langs->trans('Amount').'</td>';
    print '<td><input type="text" id="amount" name="amount" readonly class="flat right" size="10"></td>';
    print '</tr>';
    print '<tr>';
    print '<td>'.$langs->trans('NumberOfLines').'</td>';
    print '<td><input type="text" id="nbcash" name="nbcash" readonly class="flat right" size="5"></td>';
    print '</tr>';
    print '</table>';
    print '</div>';

    // Query with ORDER BY dateo ASC instead of DESC
    $sql = "SELECT DISTINCT b.rowid, b.dateo as date_operation, b.amount,";
    $sql.= " b.label as description, b.fk_type,";
    $sql.= " ba.label as bank_account_label,";
    $sql.= " p.rowid as payment_id, p.ref as payment_ref,";
    $sql.= " f.ref as invoice_ref, s.nom as thirdparty_name";
    $sql.= " FROM ".MAIN_DB_PREFIX."bank as b";
    $sql.= " INNER JOIN ".MAIN_DB_PREFIX."bank_account as ba ON b.fk_account = ba.rowid";
    $sql.= " INNER JOIN ".MAIN_DB_PREFIX."bank_url as bu ON bu.fk_bank = b.rowid";
    $sql.= " INNER JOIN ".MAIN_DB_PREFIX."paiement as p ON (bu.url_id = p.rowid AND bu.type = 'payment')";
    $sql.= " LEFT JOIN ".MAIN_DB_PREFIX."paiement_facture as pf ON p.rowid = pf.fk_paiement";
    $sql.= " LEFT JOIN ".MAIN_DB_PREFIX."facture as f ON pf.fk_facture = f.rowid";
    $sql.= " LEFT JOIN ".MAIN_DB_PREFIX."societe as s ON f.fk_soc = s.rowid";
    $sql.= " WHERE ba.courant = 2";
    $sql.= " AND (b.fk_bordereau IS NULL OR b.fk_bordereau = 0)";
    $sql.= " AND b.amount > 0";
    $sql.= " ORDER BY b.dateo ASC"; // Changed to ASC for oldest to newest

    print '<br>';
    print '<table class="noborder centpercent">';
    print '<tr class="liste_titre">';
    print '<td>'.$langs->trans("Date").'</td>';
    print '<td>'.$langs->trans("CashRegister").'</td>';
    print '<td>'.$langs->trans("Invoice").'</td>';
    print '<td>'.$langs->trans("Customer").'</td>';
    print '<td>'.$langs->trans("Description").'</td>';
    print '<td class="right">'.$langs->trans("Amount").'</td>';
    print '<td class="center">'.$langs->trans("Select").' ';
    print '<input type="checkbox" onclick="toggleAll(this)" title="'.$langs->trans("SelectAll").'/'.$langs->trans("SelectNone").'">';
    print '</td>';
    print '</tr>';

    $resql = $db->query($sql);
    if ($resql) {
        $num = $db->num_rows($resql);
        if ($num > 0) {
            while ($obj = $db->fetch_object($resql)) {
                print '<tr class="oddeven">';
                print '<td>'.dol_print_date($db->jdate($obj->date_operation), 'day').'</td>';
                print '<td>'.($obj->bank_account_label ?: '').'</td>';
                print '<td>'.($obj->invoice_ref ?: '').'</td>';
                print '<td>'.($obj->thirdparty_name ?: '').'</td>';
                print '<td>'.dol_escape_htmltag($obj->description).'</td>';
                print '<td class="right">'.price($obj->amount).'</td>';
                print '<td class="center">';
                print '<input type="checkbox" name="toRemise[]" value="'.$obj->rowid.'" ';
                print 'data-amount="'.$obj->amount.'" onclick="updateTotals()">';
                print '</td>';
                print '</tr>';
            }
        } else {
            print '<tr><td colspan="7" class="opacitymedium center">';
            print $langs->trans("NoPaymentAvailable");
            print '</td></tr>';
        }
        $db->free($resql);
    } else {
        dol_print_error($db);
    }
    print '</table>';
}

/**
 * Print cash deposit slip details
 */
function print_show_slip($object, $form)
{
    global $langs;
    
    $head = bordereau_prepare_head($object);
    print dol_get_fiche_head($head, 'card', $langs->trans("CashDepositSlip"), -1, 'payment');
    
    print '<table class="border centpercent">';
    print '<tr><td>'.$langs->trans('Ref').'</td><td>'.$object->ref.'</td></tr>';
    print '<tr><td>'.$langs->trans('Date').'</td><td>'.dol_print_date($object->date_bordereau, 'day').'</td></tr>';
    print '<tr><td>'.$langs->trans('Amount').'</td><td>'.price($object->amount).'</td></tr>';
    print '<tr><td>'.$langs->trans('NumberOfLines').'</td><td>'.$object->nbcash.'</td></tr>';
    print '<tr><td>'.$langs->trans('Note').'</td><td>'.$object->note.'</td></tr>';
    print '</table>';
    
    print dol_get_fiche_end();
    
    print_included_payments($object);
}

/**
 * Prepare the head for the bordereau (deposit slip) card
 *
 * @param Object $object Object containing the bordereau data
 * @return array Array of head elements for the tabs
 */
function bordereaux_prepare_head($object)
{
    global $langs, $conf;

    $h = 0;
    $head = array();

    $head[$h][0] = DOL_URL_ROOT.'/compta/paiement/espece/card.php?id='.$object->id;
    $head[$h][1] = $langs->trans("Card");
    $head[$h][2] = 'card';
    $h++;

    return $head;
}