<?php
/* Copyright (C) 2006		<PERSON><PERSON><PERSON>	<<EMAIL>>
 * Copyright (C) 2007-2016	<PERSON>		<<EMAIL>>
 * Copyright (C) 2009-2012	<PERSON>			<<EMAIL>>
 * Copyright (C) 2014		<PERSON>		<<EMAIL>>
 * Copyright (C) 2016		<PERSON><PERSON>   		<<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

/**
 *	\file		sinedtyi/compta/paiement/espece/card.php
 *	\ingroup	bank, invoice
 *	\brief		Page for cash deposits
 */

// Load Dolibarr environment
require '../../../main.inc.php';
require_once DOL_DOCUMENT_ROOT.'/compta/paiement/espece/class/remiseespece.class.php';
require_once DOL_DOCUMENT_ROOT.'/compta/bank/class/account.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/html.formother.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/date.lib.php';

// Load translation files required by the page
$langs->loadLangs(array('banks', 'categories', 'bills'));

// Security check
if ($user->socid) {
	$socid = $user->socid;
}
$result = restrictedArea($user, 'banque', '', '');

$search_ref = GETPOST('search_ref', 'alpha');
$search_account = GETPOST('search_account', 'int');
$search_amount = GETPOST('search_amount', 'alpha');

$limit = GETPOST('limit', 'int') ?GETPOST('limit', 'int') : $conf->liste_limit;
$sortfield = GETPOST('sortfield', 'aZ09comma');
$sortorder = GETPOST('sortorder', 'aZ09comma');
$page = GETPOSTISSET('pageplusone') ? (GETPOST('pageplusone') - 1) : GETPOST("page", 'int');
if (empty($page) || $page == -1) {
	$page = 0;
}     // If $page is not defined, or '' or -1
$offset = $limit * $page;
$pageprev = $page - 1;
$pagenext = $page + 1;
if (!$sortorder) {
	$sortorder = "DESC";
}
if (!$sortfield) {
	$sortfield = "bc.date_bordereau";
}

$year = GETPOST("year");
$month = GETPOST("month");
$optioncss = GETPOST('optioncss', 'alpha');
$view = GETPOST("view", 'alpha');

$form = new Form($db);
$formother = new FormOther($db);
$checkdepositstatic = new RemiseEspece($db);
$accountstatic = new Account($db);


/*
 * Actions
 */

// If click on purge search criteria ?
if (GETPOST('button_removefilter_x', 'alpha') || GETPOST('button_removefilter.x', 'alpha') || GETPOST('button_removefilter', 'alpha')) { // All tests are required to be compatible with all browsers
	$search_ref = '';
	$search_amount = '';
	$search_account = '';
	$year = '';
	$month = '';
}



/*
 * View
 */

$title = $langs->trans("CashReceipts");
$help_url = '';
llxHeader('', $title, $help_url);

// Add title and button
print load_fiche_titre($langs->trans("CashReceipts"), dolGetButtonTitle($langs->trans('NewCashReceipt'), '', 'fa fa-plus-circle', 'card.php?action=new'), 'bank');

// Statistics
print '<div class="fichecenter">';
print '<div class="fichethirdleft">';
print load_fiche_titre($langs->trans("Statistics"), '', '');

print '<table class="border centpercent">';
print '<tr class="liste_titre">';
print '<td>'.$langs->trans("Number").'</td>';
print '<td>'.$langs->trans("Amount").'</td>';
print '</tr>';

// Get total stats
$sql = "SELECT COUNT(*) as nb, SUM(amount) as total";
$sql.= " FROM ".MAIN_DB_PREFIX."bordereau_cash";
$sql.= " WHERE entity = ".$conf->entity;
$resql = $db->query($sql);
if ($resql) {
    $obj = $db->fetch_object($resql);
    print '<tr class="oddeven">';
    print '<td>'.$obj->nb.'</td>';
    print '<td>'.price($obj->total).'</td>';
    print '</tr>';
}
print '</table>';
print '</div>';
print '</div><br>';

// List
print '<form method="GET" action="'.$_SERVER["PHP_SELF"].'">';

// Main table
print '<table class="noborder centpercent">';

// Header
print '<tr class="liste_titre">';
print_liste_field_titre("Ref", $_SERVER["PHP_SELF"], "bc.ref", "", "", "", $sortfield, $sortorder);
print_liste_field_titre("Type", $_SERVER["PHP_SELF"], "", "", "", "", $sortfield, $sortorder);
print_liste_field_titre("Date", $_SERVER["PHP_SELF"], "bc.date_bordereau", "", "", "", $sortfield, $sortorder);
print_liste_field_titre("Account", $_SERVER["PHP_SELF"], "ba.label", "", "", "", $sortfield, $sortorder);
print_liste_field_titre("Amount", $_SERVER["PHP_SELF"], "bc.amount", "", "", 'class="right"', $sortfield, $sortorder);
print_liste_field_titre("Status", $_SERVER["PHP_SELF"], "bc.statut", "", "", 'class="right"', $sortfield, $sortorder);
print '</tr>';

// Data rows
$sql = "SELECT bc.rowid, bc.ref, bc.date_bordereau, bc.amount, bc.statut,";
$sql.= " ba.rowid as bid, ba.label";
$sql.= " FROM ".MAIN_DB_PREFIX."bordereau_cash as bc,";
$sql.= " ".MAIN_DB_PREFIX."bank_account as ba";
$sql.= " WHERE bc.fk_bank_account = ba.rowid";
$sql.= " AND bc.entity = ".$conf->entity;
$sql.= $db->order($sortfield, $sortorder);

$resql = $db->query($sql);
if ($resql) {
    $num = $db->num_rows($resql);
    
    if ($num > 0) {
        while ($obj = $db->fetch_object($resql)) {
            print '<tr class="oddeven">';
            
            print '<td class="nowrap">';
            print '<a href="card.php?id='.$obj->rowid.'">'.$obj->ref.'</a>';
            print '</td>';
            
            print '<td>'.$langs->trans("Cash").'</td>';
            print '<td>'.dol_print_date($db->jdate($obj->date_bordereau), 'day').'</td>';
            print '<td>'.$obj->label.'</td>';
            print '<td class="right">'.price($obj->amount).'</td>';
            
            print '<td class="right">';
            if ($obj->statut == 0) {
                print '<span class="badge badge-status0 badge-status" title="'.$langs->trans("Draft").'">';
                print $langs->trans("Draft");
                print '</span>';
            } else {
                print '<span class="badge badge-status4 badge-status" title="'.$langs->trans("Validated").'">';
                print $langs->trans("Validated");
                print '</span>';
            }
            print '</td>';
            
            print '</tr>';
        }
    } else {
        print '<tr><td colspan="6"><span class="opacitymedium">'.$langs->trans("NoRecordFound").'</span></td></tr>';
    }
    print "</table>";
    print '</form>';
    
    $db->free($resql);
} else {
    dol_print_error($db);
}

// End of page
llxFooter();
$db->close();
