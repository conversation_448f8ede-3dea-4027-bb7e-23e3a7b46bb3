<?php
/* Copyright (C) 2006      <PERSON><PERSON><PERSON> <<EMAIL>>
 * Copyright (C) 2007-2011 <PERSON>  <<EMAIL>>
 * Copyright (C) 2005-2009 <PERSON>        <<EMAIL>>
 * Copyright (C) 2011-2016 <PERSON><PERSON>        <<EMAIL>>
 * Copyright (C) 2015      <PERSON>        <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

/**
 *	\file       sinedtyi/compta/paiement/traite/class/bordereautraite.class.php
 *	\ingroup    compta
 *	\brief      File with class to manage traite delivery receipts
 */
require_once DOL_DOCUMENT_ROOT.'/core/class/commonobject.class.php';
require_once DOL_DOCUMENT_ROOT.'/compta/facture/class/facture.class.php';


/**
 * Class to manage traite delivery receipts
 */
class BordereauTraite extends CommonObject
{
    /**
     * @var string ID to identify managed object
     */
    public $element = 'bordereau_traite';
    
    /**
     * @var string Name of table without prefix where object is stored
     */
    public $table_element = 'bordereau_traite';

    public $rowid;
    public $datec;
    public $date_bordereau;
    public $fk_user_author;
    public $fk_bank_account;
    public $amount;
    public $ref;
    public $statut;
    public $ref_ext;
    public $note;
    public $nbtraite;
    public $entity;
    
    /**
     * @var array Fields of the table
     */
    public $fields = array(
        'rowid' => array('type'=>'integer', 'label'=>'ID', 'enabled'=>1, 'position'=>1, 'notnull'=>1, 'visible'=>0, 'primarykey'=>1),
        'datec' => array('type'=>'datetime', 'label'=>'DateCreation', 'enabled'=>1, 'position'=>10, 'notnull'=>0, 'visible'=>1),
        'date_bordereau' => array('type'=>'datetime', 'label'=>'DateBordereau', 'enabled'=>1, 'position'=>20, 'notnull'=>0, 'visible'=>1),
        'fk_user_author' => array('type'=>'integer', 'label'=>'UserAuthor', 'enabled'=>1, 'position'=>30, 'notnull'=>1, 'visible'=>1),
        'fk_bank_account' => array('type'=>'integer', 'label'=>'BankAccount', 'enabled'=>1, 'position'=>40, 'notnull'=>1, 'visible'=>1),
        'amount' => array('type'=>'double(24,8)', 'label'=>'Amount', 'enabled'=>1, 'position'=>50, 'notnull'=>0, 'visible'=>1, 'default'=>'0'),
        'ref' => array('type'=>'varchar(30)', 'label'=>'Ref', 'enabled'=>1, 'position'=>60, 'notnull'=>1, 'visible'=>1),
        'statut' => array('type'=>'smallint', 'label'=>'Status', 'enabled'=>1, 'position'=>70, 'notnull'=>0, 'visible'=>1, 'default'=>'0'),
        'ref_ext' => array('type'=>'varchar(255)', 'label'=>'RefExt', 'enabled'=>1, 'position'=>80, 'notnull'=>0, 'visible'=>1),
        'note' => array('type'=>'text', 'label'=>'Note', 'enabled'=>1, 'position'=>90, 'notnull'=>0, 'visible'=>1),
        'nbtraite' => array('type'=>'integer', 'label'=>'NbTraite', 'enabled'=>1, 'position'=>100, 'notnull'=>0, 'visible'=>1),
        'entity' => array('type'=>'integer', 'label'=>'Entity', 'enabled'=>1, 'position'=>110, 'notnull'=>1, 'visible'=>0, 'default'=>'1')
    );

    /**
     * Status constants
     */
    const STATUS_DRAFT = 0;      // Brouillon
    const STATUS_VALIDATED = 1;   // Validé
    const STATUS_CREDITED = 2;    // Crédité
    const STATUS_REFUSED = 4;     // Refusé
    const STATUS_CANCELED = 5;    // Annulé

    /**
     * Status labels
     */
    public static $statusLabel = array(
        self::STATUS_DRAFT => 'Draft',
        self::STATUS_VALIDATED => 'Validated',
        self::STATUS_CREDITED => 'Credited',
        self::STATUS_REFUSED => 'Refused',
        self::STATUS_CANCELED => 'Canceled'
    );

    /**
     * Status CSS classes
     */
    public static $statusCss = array(
        self::STATUS_DRAFT => 'status0',
        self::STATUS_VALIDATED => 'status1',
        self::STATUS_CREDITED => 'status4',
        self::STATUS_REFUSED => 'status8',
        self::STATUS_CANCELED => 'status9'
    );

    /**
     *  Constructor
     */
    public function __construct($db)
    {
        $this->db = $db;
        $this->statut = 0;
        $this->entity = 1;
    }

    /**
     * Validate data before insertion
     * @return boolean true if valid, false if not
     */
    private function validate()
    {
        $errors = array();

        if (empty($this->fk_bank_account)) {
            $errors[] = 'BankAccountRequired';
        }
        if (empty($this->amount) || !is_numeric($this->amount)) {
            $errors[] = 'InvalidAmount';
        }
        if (!isset($this->date_bordereau) || $this->date_bordereau == '') {
            $errors[] = 'DateRequired';
        }
        
        // Log validation
        $logfile = DOL_DATA_ROOT.'/dolibarr.log';
        $this->writeLog($logfile, "Validation errors check: ".(!empty($errors) ? implode(', ', $errors) : 'no errors'));
        
        if (!empty($errors)) {
            $this->error = implode(', ', $errors);
            return false;
        }
        
        return true;
    }

    /**
     * Create cash deposit in database
     * @param User $user User making creation
     * @return int <0 if KO, Id of created object if OK
     */
    public function create($user)
    {
        global $conf;
        
        dol_syslog(__METHOD__ . " start", LOG_DEBUG);
        
        $now = dol_now();
        
        $sql = "INSERT INTO ".MAIN_DB_PREFIX."bordereau_traite (";  // Changed from bordereau_cash
        $sql.= " ref,";
        $sql.= " datec,";
        $sql.= " date_bordereau,";
        $sql.= " fk_user_author,";
        $sql.= " fk_bank_account,";
        $sql.= " amount,";
        $sql.= " nbtraite,";                                       // Changed from nbcash
        $sql.= " statut,";
        $sql.= " entity";
        $sql.= ") VALUES (";
        $sql.= " '".$this->db->escape($this->ref)."',";
        $sql.= " '".$this->db->idate($now)."',";
        $sql.= " '".$this->db->idate($this->date_bordereau)."',";
        $sql.= " ".$user->id.",";
        $sql.= " ".(int) $this->fk_bank_account.",";
        $sql.= " ".(float) price2num($this->amount).",";
        $sql.= " ".(int) $this->nbtraite.",";
        $sql.= " 0,";  // Draft status
        $sql.= " ".$conf->entity;
        $sql.= ")";

        dol_syslog(__METHOD__ . " sql=" . $sql, LOG_DEBUG);
        
        $resql = $this->db->query($sql);
        if ($resql) {
            $this->id = $this->db->last_insert_id(MAIN_DB_PREFIX."bordereau_cash");
            dol_syslog(__METHOD__ . " success - id=" . $this->id, LOG_DEBUG);
            return $this->id;
        } else {
            $this->error = $this->db->lasterror();
            dol_syslog(__METHOD__ . " error=" . $this->error, LOG_ERR);
            return -1;
        }
    }

    /**
     * Charge le compte bancaire associé
     */
    public function loadBankAccount()
    {
        global $db;
        
        if ($this->fk_bank_account > 0) {
            require_once DOL_DOCUMENT_ROOT.'/compta/bank/class/account.class.php';
            $this->bank_account = new Account($db);
            $result = $this->bank_account->fetch($this->fk_bank_account);
            if ($result > 0) {
                $this->account_id = $this->bank_account->id;
                return 1;
            }
        }
        return -1;
    }

    /**
     * Load object in memory from database
     *
     * @param  int     $id    Id object
     * @param  string  $ref   Ref
     * @return int           <0 if KO, >0 if OK
     */
    public function fetch($id, $ref = '')
    {
        $sql = "SELECT t.rowid,";
        $sql.= " t.ref,";
        $sql.= " t.datec,";
        $sql.= " t.date_bordereau,";
        $sql.= " t.fk_user_author,";
        $sql.= " t.fk_bank_account,";
        $sql.= " t.amount,";
        $sql.= " t.statut,";
        $sql.= " t.nbtraite,";
        $sql.= " t.entity,";
        $sql.= " t.note";
        $sql.= " FROM ".MAIN_DB_PREFIX."bordereau_traite as t";
        
        if ($id) {
            $sql.= " WHERE t.rowid = ".(int) $id;
        } elseif ($ref) {
            $sql.= " WHERE t.ref = '".$this->db->escape($ref)."'";
        }
        
        dol_syslog(get_class($this)."::fetch", LOG_DEBUG);
        $resql = $this->db->query($sql);
        if ($resql) {
            if ($this->db->num_rows($resql)) {
                $obj = $this->db->fetch_object($resql);

                $this->id = $obj->rowid;
                $this->ref = $obj->ref;
                $this->datec = $this->db->jdate($obj->datec);
                $this->date_bordereau = $this->db->jdate($obj->date_bordereau);
                $this->fk_user_author = $obj->fk_user_author;
                $this->fk_bank_account = $obj->fk_bank_account;
                $this->amount = $obj->amount;
                $this->statut = $obj->statut;
                $this->nbtraite = $obj->nbtraite;
                $this->entity = $obj->entity;
                $this->note = $obj->note;

                $this->db->free($resql);
                return 1;
            }
            $this->db->free($resql);
            return 0;
        }
        $this->error = $this->db->lasterror();
        return -1;
    }

    /**
     * Get next reference number for traite deposit
     *
     * @return string Reference number or false if error
     */
    public function getNextNumRef()
    {
        global $conf;
        
        $prefix = 'BT';  // BT for Bordereau Traite
        $year = date('Y');
        $month = date('m');
        
        $sql = "SELECT MAX(CAST(SUBSTRING(ref FROM 9) AS SIGNED)) as max_number";
        $sql.= " FROM ".MAIN_DB_PREFIX.$this->table_element;
        $sql.= " WHERE ref LIKE '".$prefix.$year.$month."%'";
        $sql.= " AND entity = " . $conf->entity;
        
        dol_syslog("BordereauTraite::getNextNumRef sql=".$sql, LOG_DEBUG);
        
        $resql = $this->db->query($sql);
        if ($resql) {
            $obj = $this->db->fetch_object($resql);
            $max = intval($obj->max_number);
            $next = sprintf('%04d', $max + 1);
            $nextref = $prefix.$year.$month.$next;
            
            // Verify final length <= 30 chars
            if (strlen($nextref) > 30) {
                dol_syslog("Error: Reference too long ".$nextref, LOG_ERR);
                $this->error = 'ErrorRefTooLong';
                return false;
            }
            
            return $nextref;
        }
        
        $this->error = $this->db->lasterror();
        dol_syslog("BordereauTraite::getNextNumRef error ".$this->error, LOG_ERR);
        return false;
    }

    /**
     * Helper method to write to log file
     */
    private function writeLog($logfile, $message)
    {
        $timestamp = date('Y-m-d H:i:s');
        file_put_contents($logfile, "$timestamp - $message\n", FILE_APPEND);
    }

    /**
     * Return label of status
     *
     * @param   int     $mode   0=long label, 1=short label, 2=Picto + short label, 3=Picto, 4=Picto + long label
     * @return  string          Label of status
     */
    public function getLibStatut($mode = 0)
    {
        global $langs;
        
        if (empty($this->labelStatus) || empty($this->labelStatusShort)) {
            $langs->load("banks");
            $this->labelStatus = array(
                self::STATUS_DRAFT => $langs->trans('Draft'),
                self::STATUS_VALIDATED => $langs->trans('Validated'),
                self::STATUS_CREDITED => $langs->trans('Credited'),
                self::STATUS_REFUSED => $langs->trans('Refused'),
                self::STATUS_CANCELED => $langs->trans('Canceled')
            );
            $this->labelStatusShort = array(
                self::STATUS_DRAFT => $langs->trans('Draft'),
                self::STATUS_VALIDATED => $langs->trans('Validated'),
                self::STATUS_CREDITED => $langs->trans('Credited'),
                self::STATUS_REFUSED => $langs->trans('Refused'),
                self::STATUS_CANCELED => $langs->trans('Canceled')
            );
        }

        if ($mode == 0) {
            return dolGetStatus($this->labelStatus[$this->statut]);
        } elseif ($mode == 1) {
            return dolGetStatus($this->labelStatusShort[$this->statut]);
        } elseif ($mode == 2) {
            return dolGetStatus($this->labelStatusShort[$this->statut], '', 'status'.$this->statut);
        } elseif ($mode == 3) {
            return dolGetStatus('', $this->labelStatusShort[$this->statut], '', 'status'.$this->statut);
        } elseif ($mode == 4) {
            return dolGetStatus($this->labelStatus[$this->statut], $this->labelStatus[$this->statut], '', 'status'.$this->statut);
        } elseif ($mode == 5) {
            return dolGetStatus($this->labelStatusShort[$this->statut], $this->labelStatusShort[$this->statut], '', 'status'.$this->statut);
        }
    }

    /**
     * Return label of a status
     *
     * @param   int     $status Status ID
     * @param   int     $mode   0=long label, 1=short label, 2=Picto + short label, 3=Picto, 4=Picto + long label
     * @return  string          Label of status
     */
    public function LibStatut($status, $mode = 0)
    {
        global $langs;

        if ($mode == 0) {
            return $this->labelStatus[$status];
        } elseif ($mode == 1) {
            return $this->labelStatusShort[$status];
        } elseif ($mode == 2) {
            return img_picto($this->labelStatusShort[$status], 'statut'.$status).' '.$this->labelStatusShort[$status];
        } elseif ($mode == 3) {
            return img_picto($this->labelStatusShort[$status], 'statut'.$status);
        } elseif ($mode == 4) {
            return img_picto($this->labelStatusShort[$status], 'statut'.$status).' '.$this->labelStatus[$status];
        }
        return '';
    }

    /**
     * Update record
     *
     * @param  User    $user       User making update
     * @return int                 <0 if KO, >0 if OK
     */
    public function update($user)
    {
        global $conf, $langs;

        $this->db->begin();

        $sql = "UPDATE ".MAIN_DB_PREFIX."bordereau_traite";        // Changed from bordereau_cash
        $sql.= " SET statut = ".(int) $this->statut;
        $sql.= " WHERE rowid = ".(int) $this->id;

        dol_syslog(get_class($this)."::update", LOG_DEBUG);
        $resql = $this->db->query($sql);
        if ($resql) {
            $this->db->commit();
            return 1;
        } else {
            $this->error = $this->db->lasterror();
            $this->db->rollback();
            return -1;
        }
    }

    /**
     * Delete cash deposit
     *
     * @param User $user User that deletes
     * @return int <0 if KO, >0 if OK
     */
    public function delete($user)
    {
        $this->db->begin();

        // Reset bank lines
        $sql = "UPDATE ".MAIN_DB_PREFIX."bank SET fk_bordereau = NULL WHERE fk_bordereau = ".(int) $this->id;
        $resql = $this->db->query($sql);

        if ($resql) {
            // Delete bordereau
            $sql = "DELETE FROM ".MAIN_DB_PREFIX."bordereau_traite WHERE rowid = ".(int) $this->id;  // Changed from bordereau_cash
            $resql = $this->db->query($sql);
            
            if ($resql) {
                $this->db->commit();
                return 1;
            }
        }
        
        $this->error = $this->db->lasterror();
        $this->db->rollback();
        return -1;
    }

    /**
     * Generate PDF for cash deposit receipt
     *
     * @param string    $modele      Force template to use ('' by default)
     * @param Translate $outputlangs Object lang to use for translation
     * @return int                   <0 if KO, >0 if OK
     */
    public function generatePdf($modele = '', $outputlangs = '')
    {
        global $conf, $langs;

        $outputdir = $conf->bank->dir_output.'/traite';
        if (! file_exists($outputdir)) {
            dol_mkdir($outputdir);
        }

        if (empty($modele)) {
            $modele = 'blochettraite';
        }

        $file = dol_buildpath('/core/modules/traite/doc/pdf_'.$modele.'.php');  // Changed path
        
        dol_syslog("BordereauTraite::generatePdf search template=".$file." output=".$outputdir);
        
        if (file_exists($file)) {
            require_once $file;
            
            $classname = "pdf_".$modele;
            $obj = new $classname($this->db);
            
            $result = $obj->write_file($this, $outputdir, $this->ref, $outputlangs);
            if ($result > 0) {
                return 1;
            }
            $this->error = $obj->error;
            dol_syslog("BordereauTraite::generatePdf Error: ".$this->error, LOG_ERR);
        }
        
        $this->error = $langs->trans("ErrorModuleNotFound").' : '.$file;
        dol_syslog($this->error, LOG_ERR);
        return -1;
    }

    /**
     * Load payment lines into $this->lines - Not needed for cash deposits
     * as we only use bordereau_cash table
     */
    public function getLinesArray()
    {
        // We don't need lines for cash deposits
        // Everything is in bordereau_cash table
        $this->lines = array();
        return 1;
    }

    /**
     * Method to fetch bank account info
     *
     * @return  int     <0 if KO, >0 if OK
     */
    public function fetch_bank_account()
    {
        global $db;

        if ($this->fk_bank_account > 0) {
            require_once DOL_DOCUMENT_ROOT.'/compta/bank/class/account.class.php';
            $this->account = new Account($db);
            return $this->account->fetch($this->fk_bank_account);
        }
        return 0;
    }

    /**
     * Initialize with dummy values
     *
     * @return void
     */
    public function initAsSpecimen()
    {
        global $user;

        $now = dol_now();
        
        $this->id = 0;
        $this->ref = 'SPECIMEN';
        $this->specimen = 1;
        $this->statut = 0;
        $this->date_bordereau = $now;
        $this->datec = $now;
        $this->fk_user_author = $user->id;
        $this->fk_bank_account = 1;
        $this->amount = 19.99;
        $this->nbtraite = 1;
        $this->note = 'Specimen de bordereau de traite';
    }

    /**
     * Get payment lines for this bordereau
     */
    public function getPaymentLines()
    {
        global $conf;
        
        $sql = "SELECT DISTINCT p.rowid, p.datep, p.amount,";
        $sql.= " GROUP_CONCAT(DISTINCT f.ref SEPARATOR ', ') as facref,";
        $sql.= " s.nom as thirdparty,";
        $sql.= " cp.code, cp.libelle as payment_type";
        $sql.= " FROM ".MAIN_DB_PREFIX."bank b";
        $sql.= " INNER JOIN ".MAIN_DB_PREFIX."paiement p ON p.fk_bank = b.rowid";
        $sql.= " LEFT JOIN ".MAIN_DB_PREFIX."paiement_facture as pf ON p.rowid = pf.fk_paiement";
        $sql.= " LEFT JOIN ".MAIN_DB_PREFIX."facture as f ON pf.fk_facture = f.rowid";
        $sql.= " LEFT JOIN ".MAIN_DB_PREFIX."societe as s ON f.fk_soc = s.rowid";
        $sql.= " LEFT JOIN ".MAIN_DB_PREFIX."c_paiement as cp ON p.fk_paiement = cp.id";
        $sql.= " WHERE b.fk_bordereau = " . (int) $this->id;
        $sql.= " AND p.fk_paiement IN (51, 52)";  // 51=LCR, 52=TRA uniquement
        $sql.= " AND p.entity = " . $conf->entity;
        $sql.= " GROUP BY p.rowid, p.datep, p.amount, s.nom, cp.code, cp.libelle";
        $sql.= " ORDER BY p.datep";

        dol_syslog(get_class($this)."::getPaymentLines sql=".$sql, LOG_DEBUG);
        
        $resql = $this->db->query($sql);
        if ($resql) {
            $lines = array();
            while ($obj = $this->db->fetch_object($resql)) {
                $line = new stdClass();
                $line->id = $obj->rowid;
                $line->datep = $this->db->jdate($obj->datep);
                $line->amount = $obj->amount;
                $line->facref = $obj->facref;
                $line->thirdparty = $obj->thirdparty;
                $line->payment_type = $obj->payment_type;
                $line->payment_code = $obj->code;
                $line->fk_bordereau = $obj->fk_bordereau;  // Ajout du fk_bordereau
                $lines[] = $line;
                
                dol_syslog("Found payment: id=".$line->id." amount=".$line->amount." bordereau=".$line->fk_bordereau, LOG_DEBUG);
            }
            return $lines;
        }
        
        dol_syslog(get_class($this)."::getPaymentLines error=".$this->db->lasterror(), LOG_ERR);
        return array();
    }
}
?>