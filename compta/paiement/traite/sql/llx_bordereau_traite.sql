-- Table llx_bordereau_traite - <PERSON><PERSON><PERSON><PERSON> présente, on utilise CREATE IF NOT EXISTS
CREATE TABLE IF NOT EXISTS llx_bordereau_traite (
    rowid INTEGER AUTO_INCREMENT PRIMARY KEY,
    ref VARCHAR(30) NOT NULL,
    entity INTEGER DEFAULT 1 NOT NULL,
    datec DATETIME,
    date_bordereau DATETIME,
    fk_user_author INTEGER DEFAULT NULL,
    fk_bank_account INTEGER NOT NULL,
    amount DOUBLE(24,8) DEFAULT 0,
    nbtraite INTEGER DEFAULT NULL,
    statut SMALLINT DEFAULT 0,
    ref_ext VARCHAR(255) DEFAULT NULL,
    note TEXT,
    tms TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_bordereau_traite_ref (ref),
    INDEX idx_bordereau_traite_datec (datec),
    INDEX idx_bordereau_traite_date_bordereau (date_bordereau),
    INDEX idx_bordereau_traite_fk_user_author (fk_user_author),
    INDEX idx_bordereau_traite_fk_bank_account (fk_bank_account),
    INDEX idx_bordereau_traite_statut (statut)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;