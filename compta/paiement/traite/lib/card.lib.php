<?php

// Add error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

/**
 * Library for cash deposit slip card
 */

function print_create_form($object, $form, $db)
{
    global $langs;
    
    // Add JavaScript for calculations
    print getJavaScriptCalculations();
    
    print '<form method="POST" action="'.$_SERVER["PHP_SELF"].'">';
    print '<input type="hidden" name="token" value="'.newToken().'">';
    print '<input type="hidden" name="action" value="create">';
    
    // Form sections with bank account and date
    print_form_header($form);
    
    // Display totals after bank account and date
    displayTotalFields();
    
    // Display payments table
    print_payments_table($db);
    
    print '<div class="center">';
    print '<input type="submit" class="button" value="'.$langs->trans('Create').'" id="submitButton" disabled>';
    print '</div>';
    
    print '</form>';
}

/**
 * Validate form data
 */
function validate_cash_deposit_form($object)
{
    global $langs;
    $errors = array();
    
    // Bank account validation
    if (empty($object->fk_bank_account)) {
        $errors[] = $langs->trans('ErrorFieldRequired', $langs->transnoentities('BankAccount'));
    }

    // Amount validation
    if (empty($object->amount) || $object->amount <= 0) {
        $errors[] = $langs->trans('ErrorFieldRequired', $langs->transnoentities('Amount'));
    }

    // Date validation
    if (empty($object->date_bordereau)) {
        $errors[] = $langs->trans('ErrorFieldRequired', $langs->transnoentities('Date'));
    }

    // Selected lines validation
    if (empty($object->nbcash) || $object->nbcash <= 0) {
        $errors[] = $langs->trans('ErrorNoLinesSelected');
    }

    return $errors;
}

/**
 * Print form header
 *
 * @param Form $form Form object
 * @return void
 */
function print_form_header($form)
{
    global $langs, $db;

    print '<table class="border centpercent">';

    // Bank account - Filter to show only bank accounts
    print '<tr><td class="titlefieldcreate fieldrequired">'.$langs->trans('BankAccount').'</td>';
    print '<td>';
    $form->select_comptes('', 'fk_bank_account', 0, 'courant=1', 1);  // courant=1 filters for bank accounts only
    print '</td></tr>';

    // Date
    print '<tr><td class="fieldrequired">'.$langs->trans('Date').'</td>';
    print '<td>';
    print $form->selectDate('', 'date_bordereau');
    print '</td></tr>';

    print '</table>';
    print '<br>';
}

/**
 * Print payments table
 *
 * @param DoliDB $db Database handler
 * @return void
 */
function print_payments_table($db)
{
    global $langs, $conf;
    
    // Requête simplifiée et optimisée
    $sql = "SELECT DISTINCT p.rowid, p.datep, p.amount,";
    $sql.= " GROUP_CONCAT(DISTINCT f.ref SEPARATOR ', ') as facref,";
    $sql.= " s.nom as thirdparty,";
    $sql.= " cp.libelle as payment_type";
    $sql.= " FROM ".MAIN_DB_PREFIX."paiement p";
    $sql.= " LEFT JOIN ".MAIN_DB_PREFIX."bank b ON p.fk_bank = b.rowid";
    $sql.= " LEFT JOIN ".MAIN_DB_PREFIX."paiement_facture as pf ON p.rowid = pf.fk_paiement";
    $sql.= " LEFT JOIN ".MAIN_DB_PREFIX."facture as f ON pf.fk_facture = f.rowid";
    $sql.= " LEFT JOIN ".MAIN_DB_PREFIX."societe as s ON f.fk_soc = s.rowid";
    $sql.= " LEFT JOIN ".MAIN_DB_PREFIX."c_paiement as cp ON p.fk_paiement = cp.id";
    $sql.= " WHERE p.fk_paiement IN (51, 52)";  // 51=LCR, 52=TRA
    $sql.= " AND (b.fk_bordereau IS NULL OR b.fk_bordereau = 0)";
    $sql.= " AND p.entity = " . $conf->entity;
    $sql.= " GROUP BY p.rowid, p.datep, p.amount, s.nom, cp.libelle";
    $sql.= " ORDER BY p.datep DESC";

    dol_syslog("print_payments_table sql=".$sql, LOG_DEBUG);

    $resql = $db->query($sql);
    if ($resql) {
        $num = $db->num_rows($resql);

        print '<table class="noborder centpercent">';
        print '<tr class="liste_titre">';
        print '<td>'.$langs->trans("Date").'</td>';
        print '<td>'.$langs->trans("Type").'</td>';
        print '<td>'.$langs->trans("Invoice").'</td>';
        print '<td>'.$langs->trans("ThirdParty").'</td>';
        print '<td class="right">'.$langs->trans("Amount").'</td>';
        print '<td class="center">'.$langs->trans("Select").'</td>';
        print "</tr>\n";

        if ($num > 0) {
            while ($obj = $db->fetch_object($resql)) {
                print '<tr class="oddeven payment-row">';
                print '<td>'.dol_print_date($db->jdate($obj->datep), 'day').'</td>';
                print '<td>'.$obj->payment_type.'</td>';
                print '<td>'.$obj->facref.'</td>';
                print '<td>'.$obj->thirdparty.'</td>';
                // Modification ici pour ajouter le data-amount avec price2num
                print '<td class="right amount" data-amount="'.price2num($obj->amount).'">'
                    .price($obj->amount).'</td>';
                print '<td class="center">';
                print '<input type="checkbox" class="payment-checkbox" name="toRemise[]" value="'.$obj->rowid.'">';
                print '</td>';
                print "</tr>\n";
            }
        } else {
            print '<tr><td colspan="6" class="opacitymedium">'.$langs->trans("NoPaymentAvailable").'</td></tr>';
        }
        print "</table>";
        
        $db->free($resql);
    } else {
        dol_print_error($db);
    }
}

/**
 * Print cash deposit slip details
 */
function print_show_slip($object, $form)
{
    global $langs;
    
    $head = bordereau_prepare_head($object);
    print dol_get_fiche_head($head, 'card', $langs->trans("CashDepositSlip"), -1, 'payment');
    
    print '<table class="border centpercent">';
    print '<tr><td>'.$langs->trans('Ref').'</td><td>'.$object->ref.'</td></tr>';
    print '<tr><td>'.$langs->trans('Date').'</td><td>'.dol_print_date($object->date_bordereau, 'day').'</td></tr>';
    print '<tr><td>'.$langs->trans('Amount').'</td><td>'.price($object->amount).'</td></tr>';
    print '<tr><td>'.$langs->trans('NumberOfLines').'</td><td>'.$object->nbcash.'</td></tr>';
    print '<tr><td>'.$langs->trans('Note').'</td><td>'.$object->note.'</td></tr>';
    print '</table>';
    
    print dol_get_fiche_end();
    
    print_included_payments($object);
}

/**
 * Prepare the head for the bordereau (deposit slip) card
 *
 * @param Object $object Object containing the bordereau data
 * @return array Array of head elements for the tabs
 */
function bordereaux_prepare_head($object)
{
    global $langs, $conf;

    $h = 0;
    $head = array();

    $head[$h][0] = DOL_URL_ROOT.'/compta/paiement/espece/card.php?id='.$object->id;
    $head[$h][1] = $langs->trans("Card");
    $head[$h][2] = 'card';
    $h++;

    return $head;
}