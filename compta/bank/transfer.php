<?php
/* Copyright (C) 2001-2005 <PERSON><PERSON><PERSON> <<EMAIL>>
 * Copyright (C) 2004-2019 <PERSON>  <<EMAIL>>
 * Copyright (C) 2005-2015 <PERSON>        <<EMAIL>>
 * Copyright (C) 2012	   <PERSON><PERSON>        <<EMAIL>>
 * Copyright (C) 2015      <PERSON><PERSON><PERSON>	<<EMAIL>>
 * Copyright (C) 2015      <PERSON>        <<EMAIL>>
 * Copyright (C) 2018-2021 Frédéric France      <<EMAIL>>
 * Copyright (C) 2023      <PERSON><PERSON>          <<EMAIL>>
 * Copyright (C) 2023      Benjamin GREMBI         <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

/**
 *    \file       htdocs/compta/bank/transfer.php
 *    \ingroup    bank
 *    \brief      Page for entering a bank transfer
 */

// Load Dolibarr environment
require '../../main.inc.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/bank.lib.php';
require_once DOL_DOCUMENT_ROOT.'/compta/bank/class/account.class.php';

// Load translation files required by the page
$langs->loadLangs(array('banks', 'categories', 'multicurrency'));

$action = GETPOST('action', 'aZ09');

$hookmanager->initHooks(array('banktransfer'));

$socid = 0;
if ($user->socid > 0) {
	$socid = $user->socid;
}
if (!$user->rights->banque->transfer) {
	accessforbidden();
}

$MAXLINES = 10;

$error = 0;

// Ajout au début du fichier, juste après les includes
if (isset($_SESSION['transfer_success_message'])) {
    setEventMessages($_SESSION['transfer_success_message'], null, 'mesgs');
    unset($_SESSION['transfer_success_message']);
}

// Problème de sécurité potentiel - Il faut nettoyer la session après utilisation
if (isset($_SESSION['transfer_success'])) {
    setEventMessages($_SESSION['transfer_success']['message'], null, 'mesgs');
    unset($_SESSION['transfer_success']); // Nettoyer immédiatement
}

// Simplifier en une seule fonction de téléchargement
function downloadPDF($filepath, $filename) {
    if (file_exists($filepath)) {
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="'.$filename.'"');
        readfile($filepath);
        exit;
    }
    return false;
}

// Ajouter l'initialisation des variables
$tmpaccountfrom = null;
$tmpaccountto = null;
$dateo = array();
$label = array();
$amount = array();
$amountto = array();

/*
 * Actions
 */

$parameters = array('socid' => $socid);
$reshook = $hookmanager->executeHooks('doActions', $parameters, $object, $action); // Note that $action and $object may have been modified by some hooks
if ($reshook < 0) {
	setEventMessages($hookmanager->error, $hookmanager->errors, 'errors');
}
if ($action == 'add' && GETPOST('save') && !empty($user->rights->banque->transfer)) {
	$langs->load('errors');
	$i = 1;

	$dateo = array();
	$label = array();
	$amount = array();
	$amountto = array();
	$accountfrom = array();
	$accountto = array();
	$type = array();
	$tabnum = array();
	$maxtab = 1;

	while ($i < $MAXLINES) {
		$dateo[$i] = dol_mktime(12, 0, 0, GETPOST($i.'_month', 'int'), GETPOST($i.'_day', 'int'), GETPOST($i.'_year', 'int'));
		$label[$i] = GETPOST($i.'_label', 'alpha');
		$amount[$i] = price2num(GETPOST($i.'_amount', 'alpha'), 'MT', 2);
		$amountto[$i] = price2num(GETPOST($i.'_amountto', 'alpha'), 'MT', 2);
		$accountfrom[$i] = GETPOST($i.'_account_from', 'int');
		$accountto[$i] = GETPOST($i.'_account_to', 'int');
		$type[$i] = GETPOST($i.'_type', 'int');

		$tabnum[$i] = 0;
		if (!empty($label[$i]) || !($amount[$i] <= 0) || !($accountfrom[$i] < 0) || !($accountto[$i]  < 0)) {
			$tabnum[$i] = 1;
			$maxtab = $i;
		}
		$i++;
	}

	$db->begin();

	$n = 1;
	while ($n < $MAXLINES) {
		if ($tabnum[$n] === 1) {
			if ($accountfrom[$n] < 0) {
				$error++;
				setEventMessages($langs->trans("ErrorFieldRequired", '#'.$n. ' ' .$langs->transnoentities("TransferFrom")), null, 'errors');
			}
			if ($accountto[$n] < 0) {
				$error++;
				setEventMessages($langs->trans("ErrorFieldRequired", '#'.$n. ' ' .$langs->transnoentities("TransferTo")), null, 'errors');
			}
			if (!$type[$n]) {
				$error++;
				setEventMessages($langs->trans("ErrorFieldRequired", '#'.$n. ' ' .$langs->transnoentities("Type")), null, 'errors');
			}
			if (!$dateo[$n]) {
				$error++;
				setEventMessages($langs->trans("ErrorFieldRequired", '#'.$n. ' ' .$langs->transnoentities("Date")), null, 'errors');
			}

			if (!($label[$n])) {
				$error++;
				setEventMessages($langs->trans("ErrorFieldRequired", '#'.$n. ' ' . $langs->transnoentities("Description")), null, 'errors');
			}
			if (!($amount[$n])) {
				$error++;
				setEventMessages($langs->trans("ErrorFieldRequired", '#'.$n. ' ' .$langs->transnoentities("Amount")), null, 'errors');
			}

			$tmpaccountfrom = new Account($db);
			$tmpaccountfrom->fetch(GETPOST($n.'_account_from', 'int'));

			$tmpaccountto = new Account($db);
			$tmpaccountto->fetch(GETPOST($n.'_account_to', 'int'));

			if ($tmpaccountto->currency_code == $tmpaccountfrom->currency_code) {
				$amountto[$n] = $amount[$n];
			} else {
				if (!$amountto[$n]) {
					$error++;
					setEventMessages($langs->trans("ErrorFieldRequired", '#'.$n.' '.$langs->transnoentities("AmountToOthercurrency")), null, 'errors');
				}
			}
			if ($amountto[$n] < 0) {
				$error++;
				setEventMessages($langs->trans("AmountMustBePositive").' #'.$n, null, 'errors');
			}

			if ($tmpaccountto->id == $tmpaccountfrom->id) {
				$error++;
				setEventMessages($langs->trans("ErrorFromToAccountsMustDiffers").' #'.$n, null, 'errors');
			}

			if (!$error) {
				$bank_line_id_from = 0;
				$bank_line_id_to = 0;
				$result = 0;

				// By default, electronic transfert from bank to bank
				$typefrom = $type[$n];
				$typeto = $type[$n];
				if ($tmpaccountto->courant == Account::TYPE_CASH || $tmpaccountfrom->courant == Account::TYPE_CASH) {
					// This is transfer of change
					$typefrom = 'LIQ';
					$typeto = 'LIQ';
				}

				if (!$error) {
					$bank_line_id_from = $tmpaccountfrom->addline($dateo[$n], $typefrom, $label[$n], price2num(-1 * $amount[$n]), '', '', $user);
				}
				if (!($bank_line_id_from > 0)) {
					$error++;
				}
				if (!$error) {
					$bank_line_id_to = $tmpaccountto->addline($dateo[$n], $typeto, $label[$n], $amountto[$n], '', '', $user);
				}
				if (!($bank_line_id_to > 0)) {
					$error++;
				}

				if (!$error) {
					$result = $tmpaccountfrom->add_url_line($bank_line_id_from, $bank_line_id_to, DOL_URL_ROOT.'/compta/bank/line.php?rowid=', '(banktransfert)', 'banktransfert');
				}
				if (!($result > 0)) {
					$error++;
				}
				if (!$error) {
					$result = $tmpaccountto->add_url_line($bank_line_id_to, $bank_line_id_from, DOL_URL_ROOT.'/compta/bank/line.php?rowid=', '(banktransfert)', 'banktransfert');
				}
				if (!($result > 0)) {
					$error++;
				}
				if (!$error) {
					$mesgs = $langs->trans("TransferFromToDone", '{s1}', '{s2}', $amount[$n], $langs->transnoentitiesnoconv("Currency".$conf->currency));
					$mesgs = str_replace('{s1}', '<a href="bankentries_list.php?id='.$tmpaccountfrom->id.'&sortfield=b.datev,b.dateo,b.rowid&sortorder=desc">'.$tmpaccountfrom->label.'</a>', $mesgs);
					$mesgs = str_replace('{s2}', '<a href="bankentries_list.php?id='.$tmpaccountto->id.'">'.$tmpaccountto->label.'</a>', $mesgs);
					setEventMessages($mesgs, null, 'mesgs');
				} else {
					$error++;
					setEventMessages($tmpaccountfrom->error.' '.$tmpaccountto->error, null, 'errors');
				}
			}
		}
		$n++;
	}

	if (!$error) {
		$db->commit();

		// Préparation des données PDF
		require_once dirname(__FILE__).'/pdf/pdf_transfer.class.php';
		$pdf = new pdf_transfer($db);
		$format = GETPOST('pdf_format', 'alpha');
		
		$transfer_data = array(
			'date' => $dateo[$maxtab],
			'label' => $label[$maxtab],
			'amount' => price2num($amount[$maxtab]),
			'account_from' => $tmpaccountfrom->label,
			'account_to' => $tmpaccountto->label,
			'currency' => $langs->transnoentitiesnoconv("Currency".$conf->currency)
		);
		
		$result = $pdf->write_file($transfer_data, $format);

		if ($result > 0) {
			 // Nouveau format du message
			setEventMessages($langs->trans('FileGenerated').' '.$langs->trans('and').' '
				.$langs->trans('downloaded').' '.$langs->trans('under').' '
				.$langs->trans('reference').' '.$pdf->filename, null, 'mesgs');
			
			// Stockage du PDF pour téléchargement différé
			$_SESSION['pdf_to_download'] = array(
				'filepath' => $pdf->pdf_filepath,
				'filename' => $pdf->filename
			);
			
			header('Location: '.$_SERVER["PHP_SELF"]);
			exit;
		}
	} else {
		$db->rollback();
	}
}

// Gestion du succès et téléchargement
if ($action == 'success' && isset($_SESSION['transfer_success'])) {
    if ($_SESSION['transfer_success']['status'] == 'success') {
        setEventMessages($_SESSION['transfer_success']['message'], null, 'mesgs');
        // Le téléchargement est automatiquement déclenché par le format choisi dans le formulaire
        print '<script type="text/javascript">
            window.location.href = "'.$_SERVER["PHP_SELF"].'?action=download&pdf_format=" + document.getElementById("pdf_format").value;
        </script>';
    }
}

// Gestion du téléchargement avec le format choisi
if ($action == 'download' && isset($_SESSION['transfer_success'])) {
    $format = GETPOST('pdf_format', 'alpha');
    
    if (file_exists($_SESSION['transfer_success']['pdf_file'])) {
        // Régénérer le PDF avec le format choisi
        require_once dirname(__FILE__).'/pdf/pdf_transfer.class.php';
        $pdf = new pdf_transfer($db);
        $pdf->regenerate_pdf($_SESSION['transfer_success']['transfer_data'], $format);
        
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="'.$_SESSION['transfer_success']['pdf_filename'].'"');
        readfile($pdf->pdf_filepath);
        unset($_SESSION['transfer_success']);
        exit;
    }
}

// Remove standalone PDF generation as it should only happen after successful save
if ($action == 'generatepdf') {
    header("Location: ".DOL_URL_ROOT.'/compta/bank/transfer.php');
    exit;
}

// Affichage de la page puis déclenchement du téléchargement
print '<script type="text/javascript">
    document.addEventListener("DOMContentLoaded", function() {
        if (window.location.href.indexOf("?") === -1) {
            if ("' . (isset($_SESSION['pdf_to_download']) ? '1' : '0') . '" === "1") {
                window.location.href = "' . $_SERVER["PHP_SELF"] . '?action=download";
            }
        }
    });
</script>';

// Gestion du téléchargement
if ($action === 'download' && isset($_SESSION['pdf_to_download'])) {
    $file = $_SESSION['pdf_to_download'];
    if (file_exists($file['filepath'])) {
        downloadPDF($file['filepath'], $file['filename']);
        unset($_SESSION['pdf_to_download']);
        exit;
    }
}

/*
 * View
 */

$form = new Form($db);

$help_url = 'EN:Module_Banks_and_Cash|FR:Module_Banques_et_Caisses|ES:M&oacute;dulo_Bancos_y_Cajas';
$title = $langs->trans('MenuBankInternalTransfer');

llxHeader('', $title, $help_url);


print '<script type="text/javascript">
        	$(document).ready(function () {
    	  		$(".selectbankaccount").change(function() {
						console.log("We change bank account. We check if currency differs. If yes, we show multicurrency field");
						i = $(this).attr("name").replace("_account_to", "").replace("_account_from", "");
						console.log(i);
						init_page(i);
				});

				function init_page(i) {
					var atleast2differentcurrency = false;

					$(".selectbankaccount").each(function( index ) {
						// Scan all line i and set atleast2differentcurrency if there is 2 different values among all lines
	        			var account1 = $("#select"+index+"_account_from").val();
	        			var account2 = $("#select"+index+"_account_to").val();
						var currencycode1 = $("#select"+index+"_account_from option:selected").attr("data-currency-code");
						var currencycode2 = $("#select"+index+"_account_to option:selected").attr("data-currency-code");
						console.log("Set atleast2differentcurrency according to currencycode found for index="+index+" currencycode1="+currencycode1+" currencycode2="+currencycode2);

						atleast2differentcurrency = (currencycode2!==currencycode1 && currencycode1 !== undefined && currencycode2 !== undefined && currencycode2!=="" && currencycode1!=="");
						if (atleast2differentcurrency) {
							return false;
						}
					});


					if (atleast2differentcurrency) {
						console.log("We show multicurrency field");
        				$(".multicurrency").show();
        			} else {
						console.log("We hide multicurrency field");
						$(".multicurrency").hide();
					}

					// Show all linew with view=view
					$("select").each(function() {
						if( $(this).attr("view")){
							$(this).closest("tr").removeClass("hidejs").removeClass("hideobject");
						}
					});

        		}

				init_page(1);
        	});
    		</script>';


print load_fiche_titre($langs->trans("MenuBankInternalTransfer"), '', 'bank_account');

print '<span class="opacitymedium">'.$langs->trans("TransferDesc").'</span>';
print '<br><br>';

print '<form name="add" method="post" action="'.$_SERVER["PHP_SELF"].'">';
print '<input type="hidden" name="token" value="'.newToken().'">';
print '<input type="hidden" name="action" value="add">'; // Always set to 'add'

print '<div>';

print '<div class="div-table-responsive-no-min">';
print '<table id="tablemouvbank" class="noborder centpercent">';

print '<tr class="liste_titre">';
print '<th>'.$langs->trans("TransferFrom").'</th>';
print '<th>'.$langs->trans("TransferTo").'</th>';
print '<th>'.$langs->trans("Type").'</th>';
print '<th>'.$langs->trans("Date").'</th>';
print '<th>'.$langs->trans("Description").'</th>';
print '<th class="right">'.$langs->trans("Amount").'</th>';
print '<td class="hideobject multicurrency right">'.$langs->trans("AmountToOthercurrency").'</td>';
print '</tr>';

for ($i = 1 ; $i < $MAXLINES; $i++) {
	$label = '';
	$amount = '';
	$amountto = '';

	if ($error) {
		$label = GETPOST($i.'_label', 'alpha');
		$amount = GETPOST($i.'_amount', 'alpha');
		$amountto = GETPOST($i.'_amountto', 'alpha');
	}

	if ($i == 1) {
		$classi = 'numvir number'.$i;
		$classi .= ' active';
	} else {
		$classi = 'numvir number'.$i;
		$classi .= ' hidejs hideobject';
	}

	print '<tr class="oddeven nowraponall '.$classi.'"><td>';
	print img_picto('', 'bank_account', 'class="paddingright"');
	$form->select_comptes(($error ? GETPOST($i.'_account_from', 'int') : ''), $i.'_account_from', 0, '', 1, '', isModEnabled('multicurrency') ? 1 : 0, 'minwidth100');
	print '</td>';

	print '<td class="nowraponall">';
	print img_picto('', 'bank_account', 'class="paddingright"');
	$form->select_comptes(($error ? GETPOST($i.'_account_to', 'int') : ''), $i.'_account_to', 0, '', 1, '', isModEnabled('multicurrency') ? 1 : 0, 'minwidth100');
	print "</td>\n";

	// Payment mode
	print '<td class="nowraponall">';
	$idpaymentmodetransfer = dol_getIdFromCode($db, 'VIR', 'c_paiement');
	$form->select_types_paiements(($error ? GETPOST($i.'_type', 'aZ09') : $idpaymentmodetransfer), $i.'_type', '', 0, 1, 0, 0, 1, 'minwidth100');
	print "</td>\n";

	// Date
	print '<td class="nowraponall">';
	print $form->selectDate((!empty($dateo[$i]) ? $dateo[$i] : ''), $i.'_', '', '', '', 'add');
	print "</td>\n";

	// Description
	print '<td><input name="'.$i.'_label" class="flat quatrevingtpercent selectjs" type="text" value="'.dol_escape_htmltag($label).'"></td>';

	// Amount
	print '<td class="right"><input name="'.$i.'_amount" class="flat right selectjs" type="text" size="6" value="'.dol_escape_htmltag($amount).'"></td>';

	// AmountToOthercurrency
	print '<td class="hideobject multicurrency right"><input name="'.$i.'_amountto" class="flat right" type="text" size="6" value="'.dol_escape_htmltag($amountto).'"></td>';

	print '</tr>';
}

print '</table>';
print '</div>';
print '</div>';

// Nouveau conteneur centré avec flexbox
print '<div style="display: flex; justify-content: center; align-items: center; gap: 20px; margin: 20px 0;">';

// Sélecteur de format
print '<div style="display: flex; align-items: center; gap: 10px;">';
print '<label for="pdf_format">'.$langs->trans("PrintFormat").' : </label>';
print '<select name="pdf_format" id="pdf_format" class="flat minwidth100">';
print '<option value="ticket" selected>'.$langs->trans("FormatTicket").'</option>';
print '<option value="A4">'.$langs->trans("FormatA4").'</option>';
print '</select>';
print '</div>';

// Boutons
print '<div style="display: flex; align-items: center; gap: 10px;">';
print '<a id="btnincrement" class="btnTitle btnTitlePlus" onclick="increment()" title="'.dol_escape_htmltag($langs->trans("Add")).'">
        <span class="fa fa-plus-circle valignmiddle btnTitle-icon"></span>
       </a>';
print '<input type="submit" class="button" name="save" value="'.$langs->trans("Save").'">';
print '</div>';

print '</div>';

print '</form>';

print '<script type="text/javascript">
			function increment() {
				console.log("We click to show next line");
				$(".numvir").nextAll(".hidejs:first").removeClass("hidejs").removeClass("hideobject").addClass("active").show();
			}
		</script>
	 ';

// End of page
llxFooter();

$db->close();
