<?php

require_once DOL_DOCUMENT_ROOT.'/core/lib/pdf.lib.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/company.lib.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/bank.lib.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/files.lib.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/functions.lib.php';  // Ajout pour dol_decode_html()

class pdf_transfer
{
    public $error;
    public $pdf_filepath;
    public $pdf_url;
    public $filename;
    private $db;
    
    function __construct($db)
    {
        global $langs;
        $this->db = $db;
        $this->name = "transfer";
        $this->description = $langs->trans('DocModelTransferPdf');
    }

    /**
     * Régénère le PDF avec un format spécifique
     */
    public function regenerate_pdf($transfer_data, $format)
    {
        $this->format = $format;
        return $this->write_file($transfer_data);
    }
    
    function write_file($transfer_data)
    {
        global $conf, $langs, $mysoc;

        try {
            // Validation des données
            if (empty($transfer_data) || !is_array($transfer_data)) {
                throw new Exception($langs->trans('NoTransferData'));
            }

            // Formatage des données
            $date = dol_print_date($transfer_data['date'], '%d/%m/%Y');
            $amount = price2num($transfer_data['amount']);

            $pdf = pdf_getInstance();
            if (!$pdf) {
                throw new Exception($langs->trans('ErrorPdfCreationFailed'));
            }

            // Configuration du PDF avec gestion du format de page
            $pdf->SetTitle($langs->trans('TransferPDFTitle'));
            
            // Format de page personnalisé - utiliser le format spécifié ou A4 par défaut
            $format = isset($this->format) ? $this->format : 'A4';
            if ($format === 'ticket') {
                // Format ticket de caisse (57mm x longueur variable)
                $pdf->AddPage('P', array(57, 0));
                $pdf->SetAutoPageBreak(true, 10);
                $fontSize = 8;
                $cellWidth = 50;
            } else {
                // Format standard (A4, A5, etc.)
                $pdf->AddPage();
                $fontSize = 10;
                $cellWidth = 60;
            }

            // Logo adapté au format
            $logo = $conf->mycompany->dir_output.'/logos/'.$mysoc->logo;
            if (is_readable($logo)) {
                if ($format === 'ticket') {
                    // Logo plus petit pour ticket
                    $height = 15;
                } else {
                    $height = 30;
                }
                $width = 0;  // Auto-scale
                $pageWidth = $pdf->getPageWidth();
                $logoWidth = $width;
                
                $imageSize = getimagesize($logo);
                if ($imageSize) {
                    $logoWidth = $height * $imageSize[0] / $imageSize[1];
                }
                
                $x = ($pageWidth - $logoWidth) / 2;
                $pdf->Image($logo, $x, 10, $width, $height);
                $pdf->Ln($height + 5);
            }

            // Titre adapté
            $pdf->SetFont('', 'B', $format === 'ticket' ? 12 : 16);
            $pdf->Cell(0, 10, $langs->trans('TransferPDFTitle'), 0, 1, 'C');
            $pdf->Ln(5);

            // Corps du document
            $pdf->SetFont('', 'B', $fontSize + 1);
            
            // Contenu adapté au format
            $this->addContent($pdf, $transfer_data, $format, $fontSize, $cellWidth);

            // Sauvegarde
            $dir = DOL_DATA_ROOT.'/bank/transfer';
            dol_mkdir($dir);

            $this->filename = 'transfer_'.dol_print_date(dol_now(), '%Y%m%d%H%M%S').'.pdf';
            $this->pdf_filepath = $dir.'/'.$this->filename;
            
            $pdf->Output($this->pdf_filepath, 'F');
            
            return 1;
        } catch (Exception $e) {
            $this->error = $e->getMessage();
            dol_syslog(get_class($this)."::write_file ".$this->error, LOG_ERR);
            return -1;
        }
    }

    /**
     * Ajoute le contenu adapté au format
     */
    private function addContent($pdf, $transfer_data, $format, $fontSize, $cellWidth)
    {
        global $langs;

        // Format et affichage des données
        $date = dol_print_date($transfer_data['date'], '%d/%m/%Y');
        $amount = price2num($transfer_data['amount']);

        $fields = array(
            'TransferDate' => $date,
            'TransferAmount' => price($amount).' '.$transfer_data['currency'],
            'TransferFrom' => $transfer_data['account_from'],
            'TransferTo' => $transfer_data['account_to']
        );

        foreach ($fields as $label => $value) {
            if ($format === 'ticket') {
                // Format ticket : une ligne par information
                $pdf->SetFont('', 'B', $fontSize);
                $pdf->Cell(0, 4, $langs->trans($label), 0, 1);
                $pdf->SetFont('', '', $fontSize);
                $pdf->Cell(0, 4, $value, 0, 1);
                $pdf->Ln(2);
            } else {
                // Format standard
                $pdf->SetFont('', 'B', $fontSize + 1);
                $pdf->Cell($cellWidth, 6, $langs->trans($label).' :', 0);
                $pdf->SetFont('', '', $fontSize);
                $pdf->Cell(0, 6, $value, 0);
                $pdf->Ln(8);
            }
        }

        // Description
        if (!empty($transfer_data['label'])) {
            if ($format === 'ticket') {
                $pdf->SetFont('', 'B', $fontSize);
                $pdf->Cell(0, 4, $langs->trans('Description'), 0, 1);
                $pdf->SetFont('', '', $fontSize);
                $pdf->MultiCell(0, 4, $transfer_data['label'], 0);
                $pdf->Ln(2);
            } else {
                $pdf->SetFont('', 'B', $fontSize + 1);
                $pdf->Cell($cellWidth, 6, $langs->trans('Description').' :', 0);
                $pdf->SetFont('', '', $fontSize);
                $pdf->MultiCell(0, 6, $transfer_data['label'], 0);
            }
        }

        // Signatures adaptées au format
        $pdf->Ln($format === 'ticket' ? 10 : 20);
        $this->addSignatures($pdf, $format, $fontSize);
    }

    /**
     * Ajoute les signatures adaptées au format
     */
    private function addSignatures($pdf, $format, $fontSize)
    {
        global $langs;

        $depositor = html_entity_decode($langs->trans('Depositor'), ENT_QUOTES | ENT_HTML5, 'UTF-8');
        $receiver = html_entity_decode($langs->trans('Receiver'), ENT_QUOTES | ENT_HTML5, 'UTF-8');

        if ($format === 'ticket') {
            // Format ticket : signatures l'une sous l'autre
            $pdf->SetFont('', 'B', $fontSize);
            $pdf->Cell(0, 4, $depositor.' :', 0, 1);
            $pdf->Cell(0, 10, '_________________', 0, 1, 'C');
            $pdf->Ln(5);
            $pdf->Cell(0, 4, $receiver.' :', 0, 1);
            $pdf->Cell(0, 10, '_________________', 0, 1, 'C');
        } else {
            // Format standard : signatures côte à côte
            $pdf->SetFont('', 'B', $fontSize + 1);
            $pdf->Cell(90, 6, $depositor.' :', 0, 0);
            $pdf->Cell(90, 6, $receiver.' :', 0, 1);
            $pdf->Ln(25);
            $pdf->SetFont('', '', $fontSize);
            $pdf->Cell(90, 6, '--------------------------------', 0, 0, 'C');
            $pdf->Cell(90, 6, '--------------------------------', 0, 1, 'C');
        }
    }

    /**
     * Ajoute les informations du transfert au PDF
     */
    private function addTransferInfo($pdf, $data)
    {
        global $langs;

        $fields = array(
            'TransferDate' => dol_print_date($data['date'], 'day', 'tzuser'),
            'TransferAmount' => price($data['amount']) . ' ' . $data['currency'],
            'TransferFrom' => $data['account_from'],
            'TransferTo' => $data['account_to']
        );

        foreach ($fields as $label => $value) {
            $pdf->SetFont('', 'B', 11);
            $pdf->Cell(60, 6, $langs->trans($label).' : ', 0);
            $pdf->SetFont('', '', 10);
            $pdf->Cell(0, 6, $value, 0);
            $pdf->Ln(8);
        }

        if (!empty($data['label'])) {
            $pdf->SetFont('', 'B', 11);
            $pdf->Cell(60, 6, $langs->trans('Description').' : ', 0);
            $pdf->SetFont('', '', 10);
            $pdf->MultiCell(0, 6, $data['label'], 0);
        }
    }
}
