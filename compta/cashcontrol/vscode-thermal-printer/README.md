# VSCode Thermal Printer Extension

## Overview

The VSCode Thermal Printer Extension provides a seamless way to manage thermal printing directly from your development environment. This extension allows you to connect to thermal printers, send print jobs, and manage printer settings efficiently.

## Features

- Connect and disconnect from thermal printers.
- Send print jobs with formatted data.
- Easy integration with command palette for quick access to printing commands.

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/vscode-thermal-printer.git
   ```

2. Navigate to the project directory:
   ```bash
   cd vscode-thermal-printer
   ```

3. Install the dependencies:
   ```bash
   npm install
   ```

4. Open the project in your code editor.

## Usage

- Open the command palette (Ctrl+Shift+P or Cmd+Shift+P).
- Type `Thermal Printer` to see the available commands.
- Use the commands to connect to your printer and send print jobs.

## Contributing

Contributions are welcome! Please open an issue or submit a pull request for any enhancements or bug fixes.

## License

This project is licensed under the MIT License. See the LICENSE file for details.