import * as net from 'net';

export class PrinterService {
    private client: net.Socket | null = null;

    constructor(private host: string, private port: number) {}

    public connect(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.client = new net.Socket();
            this.client.connect(this.port, this.host, () => {
                console.log('Connected to printer');
                resolve();
            });

            this.client.on('error', (err) => {
                console.error('Connection error:', err);
                reject(err);
            });
        });
    }

    public disconnect(): Promise<void> {
        return new Promise((resolve, reject) => {
            if (this.client) {
                this.client.end(() => {
                    console.log('Disconnected from printer');
                    resolve();
                });

                this.client.on('error', (err) => {
                    console.error('Disconnection error:', err);
                    reject(err);
                });
            } else {
                resolve();
            }
        });
    }

    public print(data: string): Promise<void> {
        return new Promise((resolve, reject) => {
            if (this.client) {
                this.client.write(data, (err) => {
                    if (err) {
                        console.error('Print error:', err);
                        reject(err);
                    } else {
                        console.log('Print job sent');
                        resolve();
                    }
                });
            } else {
                reject(new Error('Printer not connected'));
            }
        });
    }
}