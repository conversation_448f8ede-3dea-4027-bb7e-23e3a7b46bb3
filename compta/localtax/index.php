<?php
/* Copyright (C) 2011-2014  <PERSON><PERSON>           <<EMAIL>>
 * Copyright (C) 2014       <PERSON><PERSON><PERSON>           <<EMAIL>>
 * Copyright (C) 2018       <PERSON>     <<EMAIL>>
 * Copyright (C) 2018       Frédéric France         <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */


/**
 *      \file       htdocs/compta/localtax/index.php
 *      \ingroup    tax
 *      \brief      Index page of localtax reports
 */

require '../../main.inc.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/report.lib.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/tax.lib.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/date.lib.php';
require_once DOL_DOCUMENT_ROOT.'/compta/tva/class/tva.class.php';
require_once DOL_DOCUMENT_ROOT.'/compta/localtax/class/localtax.class.php';

// Load translation files required by the page
$langs->loadLangs(array("other", "compta", "banks", "bills", "companies", "product", "trips", "admin"));

$localTaxType = GETPOST('localTaxType', 'int');

// Date range
$year = GETPOST("year", "int");
if (empty($year)) {
	$year_current = dol_print_date(dol_now('gmt'), "%Y", 'gmt');
	$year_start = $year_current;
} else {
	$year_current = $year;
	$year_start = $year;
}
$date_start = dol_mktime(0, 0, 0, GETPOST("date_startmonth"), GETPOST("date_startday"), GETPOST("date_startyear"));
$date_end = dol_mktime(23, 59, 59, GETPOST("date_endmonth"), GETPOST("date_endday"), GETPOST("date_endyear"));
if (empty($date_start) || empty($date_end)) { // We define date_start and date_end
	$q = GETPOST("q", "int");
	if (empty($q)) {
		if (GETPOST("month", "int")) {
			$date_start = dol_get_first_day($year_start, GETPOST("month", "int"), false);
			$date_end = dol_get_last_day($year_start, GETPOST("month", "int"), false);
		} else {
			$date_start = dol_get_first_day($year_start, $conf->global->SOCIETE_FISCAL_MONTH_START, false);
			$date_end = dol_time_plus_duree($date_start, 1, 'y') - 1;
		}
	} else {
		if ($q == 1) {
			$date_start = dol_get_first_day($year_start, 1, false);
			$date_end = dol_get_last_day($year_start, 3, false);
		}
		if ($q == 2) {
			$date_start = dol_get_first_day($year_start, 4, false);
			$date_end = dol_get_last_day($year_start, 6, false);
		}
		if ($q == 3) {
			$date_start = dol_get_first_day($year_start, 7, false);
			$date_end = dol_get_last_day($year_start, 9, false);
		}
		if ($q == 4) {
			$date_start = dol_get_first_day($year_start, 10, false);
			$date_end = dol_get_last_day($year_start, 12, false);
		}
	}
}

// Define modetax (0 or 1)
// 0=normal, 1=option vat for services is on debit, 2=option on payments for products
$modetax = getDolGlobalString('TAX_MODE');
if (GETPOSTISSET("modetax")) {
	$modetax = GETPOST("modetax", 'int');
}
if (empty($modetax)) {
	$modetax = 0;
}

// Security check
$socid = GETPOST('socid', 'int');
if ($user->socid) {
	$socid = $user->socid;
}
$result = restrictedArea($user, 'tax', '', '', 'charges');


/**
 * print function
 *
 * @param		DoliDB	$db		Database handler
 * @param		string	$sql	SQL Request
 * @param		string	$date	Date
 * @return		void
 */
function pt($db, $sql, $date)
{
	global $conf, $bc, $langs;

	$result = $db->query($sql);
	if ($result) {
		$num = $db->num_rows($result);
		$i = 0;
		$total = 0;
		print '<table class="noborder centpercent">';

		print '<tr class="liste_titre">';
		print '<td class="nowrap">'.$date.'</td>';
		print '<td class="right">'.$langs->trans("ClaimedForThisPeriod").'</td>';
		print '<td class="right">'.$langs->trans("PaidDuringThisPeriod").'</td>';
		print "</tr>\n";

		$totalclaimed = 0;
		$totalpaid = 0;
		$amountclaimed = 0;
		$amountpaid = 0;
		$previousmonth = '';
		$previousmode = '';
		$mode = '';

		while ($i < $num) {
			$obj = $db->fetch_object($result);
			$mode = $obj->mode;

			//print $obj->dm.' '.$obj->mode.' '.$previousmonth.' '.$previousmode;
			if ($obj->mode == 'claimed' && !empty($previousmode)) {
				print '<tr class="oddeven">';
				print '<td class="nowrap">'.$previousmonth."</td>\n";
				print '<td class="nowrap right">'.price($amountclaimed)."</td>\n";
				print '<td class="nowrap right">'.price($amountpaid)."</td>\n";
				print "</tr>\n";

				$amountclaimed = 0;
				$amountpaid = 0;
			}

			if ($obj->mode == 'claimed') {
				$amountclaimed = $obj->mm;
				$totalclaimed = $totalclaimed + $amountclaimed;
			}
			if ($obj->mode == 'paid') {
				$amountpaid = $obj->mm;
				$totalpaid = $totalpaid + $amountpaid;
			}

			if ($obj->mode == 'paid') {
				print '<tr class="oddeven">';
				print '<td class="nowrap">'.$obj->dm."</td>\n";
				print '<td class="nowrap right">'.price($amountclaimed)."</td>\n";
				print '<td class="nowrap right">'.price($amountpaid)."</td>\n";
				print "</tr>\n";
				$amountclaimed = 0;
				$amountpaid = 0;
				$previousmode = '';
				$previousmonth = '';
			} else {
				$previousmode = $obj->mode;
				$previousmonth = $obj->dm;
			}

			$i++;
		}

		if ($mode == 'claimed' && !empty($previousmode)) {
			print '<tr class="oddeven">';
			print '<td class="nowrap">'.$previousmonth."</td>\n";
			print '<td class="nowrap right">'.price($amountclaimed)."</td>\n";
			print '<td class="nowrap right">'.price($amountpaid)."</td>\n";
			print "</tr>\n";

			$amountclaimed = 0;
			$amountpaid = 0;
		}

		print '<tr class="liste_total">';
		print '<td class="right">'.$langs->trans("Total").'</td>';
		print '<td class="nowrap right">'.price($totalclaimed).'</td>';
		print '<td class="nowrap right">'.price($totalpaid).'</td>';
		print "</tr>";

		print "</table>";
		$db->free($result);
	} else {
		dol_print_error($db);
	}
}

if (empty($localTaxType)) {
	accessforbidden('Parameter localTaxType is missing');
	exit;
}


/*
 * Actions
 */

// None


/*
 * View
 */

$form = new Form($db);
$company_static = new Societe($db);
$tva = new Tva($db);

if ($localTaxType == 1) {
	$LT = 'LT1';
	$LTSummary = 'LT1Summary';
	$LTPaid = 'LT1Paid';
	$LTCustomer = 'LT1Customer';
	$LTSupplier = 'LT1Supplier';
	$CalcLT = getDolGlobalString('MAIN_INFO_LOCALTAX_CALC1');
} else {
	$LT = 'LT2';
	$LTSummary = 'LT2Summary';
	$LTPaid = 'LT2Paid';
	$LTCustomer = 'LT2Customer';
	$LTSupplier = 'LT2Supplier';
	$CalcLT = getDolGlobalString('MAIN_INFO_LOCALTAX_CALC2');
}

$fsearch = '<!-- hidden fields for form -->';
$fsearch .= '<input type="hidden" name="token" value="'.newToken().'">';
$fsearch .= '<input type="hidden" name="localTaxType" value="'.$localTaxType.'">';
$fsearch .= '<input type="hidden" name="modetax" value="'.$modetax.'">';

$description = $fsearch;

// Show report header
$name = $langs->transcountry($localTaxType == 1 ? "LT1ReportByMonth" : "LT2ReportByMonth", $mysoc->country_code);
$description .= $langs->trans($LT);
$calcmode = $langs->trans("LTReportBuildWithOptionDefinedInModule").' ';
$calcmode .= ' <span class="opacitymedium">('.$langs->trans("TaxModuleSetupToModifyRulesLT", DOL_URL_ROOT.'/admin/company.php').')</span>';

//if (!empty($conf->global->MAIN_MODULE_ACCOUNTING)) $description.='<br>'.$langs->trans("ThisIsAnEstimatedValue");

$period = $form->selectDate($date_start, 'date_start', 0, 0, 0, '', 1, 0).' - '.$form->selectDate($date_end, 'date_end', 0, 0, 0, '', 1, 0);

$builddate = dol_now();


llxHeader('', $name);

//$textprevyear="<a href=\"index.php?localTaxType=".$localTaxType."&year=" . ($year_current-1) . "\">".img_previous()."</a>";
//$textnextyear=" <a href=\"index.php?localTaxType=".$localTaxType."&year=" . ($year_current+1) . "\">".img_next()."</a>";
//print load_fiche_titre($langs->transcountry($LT,$mysoc->country_code),"$textprevyear ".$langs->trans("Year")." $year_start $textnextyear", 'bill');

$periodlink = '';
$exportlink = '';

report_header($name, '', $period, $periodlink, $description, $builddate, $exportlink, array(), $calcmode);
//report_header($name,'',$textprevyear.$langs->trans("Year")." ".$year_start.$textnextyear,'',$description,$builddate,$exportlink,array(),$calcmode);


print '<br>';

print '<div class="fichecenter"><div class="fichethirdleft">';

print load_fiche_titre($langs->transcountry($LTSummary, $mysoc->country_code), '', '');

print '<table class="noborder centpercent">';
print '<tr class="liste_titre">';
print '<td>'.$langs->trans("Year")."</td>";
if ($CalcLT == 0) {
	print '<td class="right">'.$langs->transcountry($LTCustomer, $mysoc->country_code).'</td>';
	print '<td class="right">'.$langs->transcountry($LTSupplier, $mysoc->country_code).'</td>';
}
if ($CalcLT == 1) {
	print '<td class="right">'.$langs->transcountry($LTSupplier, $mysoc->country_code).'</td><td></td>';
}
if ($CalcLT == 2) {
	print '<td class="right">'.$langs->transcountry($LTCustomer, $mysoc->country_code).'</td><td></td>';
}
print '<td class="right">'.$langs->trans("TotalToPay").'</td>';
print "<td>&nbsp;</td>\n";
print "</tr>\n";

$tmp = dol_getdate($date_start);
$y = $tmp['year'];
$m = $tmp['mon'];
$tmp = dol_getdate($date_end);
$yend = $tmp['year'];
$mend = $tmp['mon'];

$total = 0;
$subtotalcoll = 0;
$subtotalpaid = 0;
$subtotal = 0;
$i = 0;
$mcursor = 0;
while ((($y < $yend) || ($y == $yend && $m <= $mend)) && $mcursor < 1000) {	// $mcursor is to avoid too large loop
	//$m = $conf->global->SOCIETE_FISCAL_MONTH_START + ($mcursor % 12);
	if ($m == 13) {
		$y++;
	}
	if ($m > 12) {
		$m -= 12;
	}
	$mcursor++;

	// Get array with details of each line
	$x_coll = tax_by_rate(($localTaxType == 1 ? 'localtax1' : 'localtax2'), $db, $y, 0, 0, 0, $modetax, 'sell', $m);
	$x_paye = tax_by_rate(($localTaxType == 1 ? 'localtax1' : 'localtax2'), $db, $y, 0, 0, 0, $modetax, 'buy', $m);

	$x_both = array();
	//now, from these two arrays, get another array with one rate per line
	foreach (array_keys($x_coll) as $my_coll_rate) {
		$x_both[$my_coll_rate]['coll']['totalht'] = $x_coll[$my_coll_rate]['totalht'];
		$x_both[$my_coll_rate]['coll']['vat'] = $x_coll[$my_coll_rate]['vat'];
		$x_both[$my_coll_rate]['coll']['localtax1']	 = $x_coll[$my_coll_rate]['localtax1'];
		$x_both[$my_coll_rate]['coll']['localtax2']	 = $x_coll[$my_coll_rate]['localtax2'];
		$x_both[$my_coll_rate]['paye']['totalht'] = 0;
		$x_both[$my_coll_rate]['paye']['vat'] = 0;
		$x_both[$my_coll_rate]['paye']['localtax1'] = 0;
		$x_both[$my_coll_rate]['paye']['localtax2'] = 0;
		$x_both[$my_coll_rate]['coll']['links'] = '';
		$x_both[$my_coll_rate]['coll']['detail'] = array();
		foreach ($x_coll[$my_coll_rate]['facid'] as $id => $dummy) {
			//$invoice_customer->id=$x_coll[$my_coll_rate]['facid'][$id];
			//$invoice_customer->ref=$x_coll[$my_coll_rate]['facnum'][$id];
			//$invoice_customer->type=$x_coll[$my_coll_rate]['type'][$id];
			//$company_static->fetch($x_coll[$my_coll_rate]['company_id'][$id]);
			$x_both[$my_coll_rate]['coll']['detail'][] = array(
			'id'        =>$x_coll[$my_coll_rate]['facid'][$id],
			'descr'     =>$x_coll[$my_coll_rate]['descr'][$id],
			'pid'       =>$x_coll[$my_coll_rate]['pid'][$id],
			'pref'      =>$x_coll[$my_coll_rate]['pref'][$id],
			'ptype'     =>$x_coll[$my_coll_rate]['ptype'][$id],
			'payment_id'=>$x_coll[$my_coll_rate]['payment_id'][$id],
			'payment_amount'=>$x_coll[$my_coll_rate]['payment_amount'][$id],
			'ftotal_ttc'=>$x_coll[$my_coll_rate]['ftotal_ttc'][$id],
			'dtotal_ttc'=>$x_coll[$my_coll_rate]['dtotal_ttc'][$id],
			'dtype'     =>$x_coll[$my_coll_rate]['dtype'][$id],
			'datef'     =>$x_coll[$my_coll_rate]['datef'][$id],
			'datep'     =>$x_coll[$my_coll_rate]['datep'][$id],
			//'company_link'=>$company_static->getNomUrl(1,'',20),
			'ddate_start'=>$x_coll[$my_coll_rate]['ddate_start'][$id],
			'ddate_end'  =>$x_coll[$my_coll_rate]['ddate_end'][$id],

			'totalht'   =>$x_coll[$my_coll_rate]['totalht_list'][$id],
			'vat'       =>$x_coll[$my_coll_rate]['vat_list'][$id],
			'localtax1' =>$x_coll[$my_coll_rate]['localtax1_list'][$id],
			'localtax2' =>$x_coll[$my_coll_rate]['localtax2_list'][$id],
			//'link'      =>$invoice_customer->getNomUrl(1,'',12)
			);
		}
	}

	// tva paid
	foreach (array_keys($x_paye) as $my_paye_rate) {
		$x_both[$my_paye_rate]['paye']['totalht'] = $x_paye[$my_paye_rate]['totalht'];
		$x_both[$my_paye_rate]['paye']['vat'] = $x_paye[$my_paye_rate]['vat'];
		$x_both[$my_paye_rate]['paye']['localtax1'] = $x_paye[$my_paye_rate]['localtax1'];
		$x_both[$my_paye_rate]['paye']['localtax2'] = $x_paye[$my_paye_rate]['localtax2'];
		if (!isset($x_both[$my_paye_rate]['coll']['totalht'])) {
			$x_both[$my_paye_rate]['coll']['totalht'] = 0;
			$x_both[$my_paye_rate]['coll']['vat'] = 0;
			$x_both[$my_paye_rate]['coll']['localtax1'] = 0;
			$x_both[$my_paye_rate]['coll']['localtax2'] = 0;
		}
		$x_both[$my_paye_rate]['paye']['links'] = '';
		$x_both[$my_paye_rate]['paye']['detail'] = array();

		foreach ($x_paye[$my_paye_rate]['facid'] as $id => $dummy) {
			// ExpenseReport
			if ($x_paye[$my_paye_rate]['ptype'][$id] == 'ExpenseReportPayment') {
				//$expensereport->id=$x_paye[$my_paye_rate]['facid'][$id];
				//$expensereport->ref=$x_paye[$my_paye_rate]['facnum'][$id];
				//$expensereport->type=$x_paye[$my_paye_rate]['type'][$id];

				$x_both[$my_paye_rate]['paye']['detail'][] = array(
				'id'				=>$x_paye[$my_paye_rate]['facid'][$id],
				'descr'				=>$x_paye[$my_paye_rate]['descr'][$id],
				'pid'				=>$x_paye[$my_paye_rate]['pid'][$id],
				'pref'				=>$x_paye[$my_paye_rate]['pref'][$id],
				'ptype'				=>$x_paye[$my_paye_rate]['ptype'][$id],
				'payment_id'		=>$x_paye[$my_paye_rate]['payment_id'][$id],
				'payment_amount'	=>$x_paye[$my_paye_rate]['payment_amount'][$id],
				'ftotal_ttc'		=>price2num($x_paye[$my_paye_rate]['ftotal_ttc'][$id]),
				'dtotal_ttc'		=>price2num($x_paye[$my_paye_rate]['dtotal_ttc'][$id]),
				'dtype'				=>$x_paye[$my_paye_rate]['dtype'][$id],
				'ddate_start'		=>$x_paye[$my_paye_rate]['ddate_start'][$id],
				'ddate_end'			=>$x_paye[$my_paye_rate]['ddate_end'][$id],

				'totalht'			=>price2num($x_paye[$my_paye_rate]['totalht_list'][$id]),
				'vat'				=>$x_paye[$my_paye_rate]['vat_list'][$id],
				'localtax1'			=>$x_paye[$my_paye_rate]['localtax1_list'][$id],
				'localtax2'			=>$x_paye[$my_paye_rate]['localtax2_list'][$id],
				//'link'				=>$expensereport->getNomUrl(1)
				);
			} else {
				//$invoice_supplier->id=$x_paye[$my_paye_rate]['facid'][$id];
				//$invoice_supplier->ref=$x_paye[$my_paye_rate]['facnum'][$id];
				//$invoice_supplier->type=$x_paye[$my_paye_rate]['type'][$id];
				//$company_static->fetch($x_paye[$my_paye_rate]['company_id'][$id]);
				$x_both[$my_paye_rate]['paye']['detail'][] = array(
				'id'        =>$x_paye[$my_paye_rate]['facid'][$id],
				'descr'     =>$x_paye[$my_paye_rate]['descr'][$id],
				'pid'       =>$x_paye[$my_paye_rate]['pid'][$id],
				'pref'      =>$x_paye[$my_paye_rate]['pref'][$id],
				'ptype'     =>$x_paye[$my_paye_rate]['ptype'][$id],
				'payment_id'=>$x_paye[$my_paye_rate]['payment_id'][$id],
				'payment_amount'=>$x_paye[$my_paye_rate]['payment_amount'][$id],
				'ftotal_ttc'=>price2num($x_paye[$my_paye_rate]['ftotal_ttc'][$id]),
				'dtotal_ttc'=>price2num($x_paye[$my_paye_rate]['dtotal_ttc'][$id]),
				'dtype'     =>$x_paye[$my_paye_rate]['dtype'][$id],
				'datef'     =>$x_paye[$my_paye_rate]['datef'][$id],
				'datep'     =>$x_paye[$my_paye_rate]['datep'][$id],
				//'company_link'=>$company_static->getNomUrl(1,'',20),
				'ddate_start'=>$x_paye[$my_paye_rate]['ddate_start'][$id],
				'ddate_end'  =>$x_paye[$my_paye_rate]['ddate_end'][$id],

				'totalht'   =>price2num($x_paye[$my_paye_rate]['totalht_list'][$id]),
				'vat'       =>$x_paye[$my_paye_rate]['vat_list'][$id],
				'localtax1' =>$x_paye[$my_paye_rate]['localtax1_list'][$id],
				'localtax2' =>$x_paye[$my_paye_rate]['localtax2_list'][$id],
				//'link'      =>$invoice_supplier->getNomUrl(1,'',12)
				);
			}
		}
	}
	//now we have an array (x_both) indexed by rates for coll and paye

	$action = "tva";
	$object = array(&$x_coll, &$x_paye, &$x_both);
	$parameters["mode"] = $modetax;
	$parameters["year"] = $y;
	$parameters["month"] = $m;
	$parameters["type"] = 'localtax'.$localTaxType;

	// Initialize technical object to manage hooks of expenses. Note that conf->hooks_modules contains array array
	$hookmanager->initHooks(array('externalbalance'));
	$reshook = $hookmanager->executeHooks('addVatLine', $parameters, $object, $action); // Note that $action and $object may have been modified by some hooks


	print '<tr class="oddeven">';
	print '<td class="nowrap"><a href="'.DOL_URL_ROOT.'/compta/localtax/quadri_detail.php?leftmenu=tax_vat&month='.$m.'&year='.$y.'">'.dol_print_date(dol_mktime(0, 0, 0, $m, 1, $y), "%b %Y").'</a></td>';

	$x_coll_sum = 0;
	foreach (array_keys($x_coll) as $rate) {
		$subtot_coll_total_ht = 0;
		$subtot_coll_vat = 0;

		foreach ($x_both[$rate]['coll']['detail'] as $index => $fields) {
			// Payment
			$ratiopaymentinvoice = 1;
			if ($modetax != 1) {
				// Define type
				// We MUST use dtype (type in line). We can use something else, only if dtype is really unknown.
				$type = (isset($fields['dtype']) ? $fields['dtype'] : $fields['ptype']);
				// Try to enhance type detection using date_start and date_end for free lines where type
				// was not saved.
				if (!empty($fields['ddate_start'])) {
					$type = 1;
				}
				if (!empty($fields['ddate_end'])) {
					$type = 1;
				}

				if (($type == 0 && getDolGlobalString('TAX_MODE_SELL_PRODUCT') == 'invoice')
					|| ($type == 1 && getDolGlobalString('TAX_MODE_SELL_SERVICE') == 'invoice')) {
					//print $langs->trans("NA");
				} else {
					if (isset($fields['payment_amount']) && price2num($fields['ftotal_ttc'])) {
						$ratiopaymentinvoice = ($fields['payment_amount'] / $fields['ftotal_ttc']);
					}
				}
			}
			//var_dump('type='.$type.' '.$fields['totalht'].' '.$ratiopaymentinvoice);
			$temp_ht = $fields['totalht'] * $ratiopaymentinvoice;
			$temp_vat = $fields['localtax'.$localTaxType] * $ratiopaymentinvoice;
			$subtot_coll_total_ht += $temp_ht;
			$subtot_coll_vat      += $temp_vat;
			$x_coll_sum           += $temp_vat;
		}
	}
	print '<td class="nowrap right">'.price(price2num($x_coll_sum, 'MT')).'</td>';

	$x_paye_sum = 0;
	foreach (array_keys($x_paye) as $rate) {
		$subtot_paye_total_ht = 0;
		$subtot_paye_vat = 0;

		foreach ($x_both[$rate]['paye']['detail'] as $index => $fields) {
			// Payment
			$ratiopaymentinvoice = 1;
			if ($modetax != 1) {
				// Define type
				// We MUST use dtype (type in line). We can use something else, only if dtype is really unknown.
				$type = (isset($fields['dtype']) ? $fields['dtype'] : $fields['ptype']);
				// Try to enhance type detection using date_start and date_end for free lines where type
				// was not saved.
				if (!empty($fields['ddate_start'])) {
					$type = 1;
				}
				if (!empty($fields['ddate_end'])) {
					$type = 1;
				}

				if (($type == 0 && getDolGlobalString('TAX_MODE_SELL_PRODUCT') == 'invoice')
					|| ($type == 1 && getDolGlobalString('TAX_MODE_SELL_SERVICE') == 'invoice')) {
					//print $langs->trans("NA");
				} else {
					if (isset($fields['payment_amount']) && price2num($fields['ftotal_ttc'])) {
						$ratiopaymentinvoice = ($fields['payment_amount'] / $fields['ftotal_ttc']);
					}
				}
			}
			//var_dump('type='.$type.' '.$fields['totalht'].' '.$ratiopaymentinvoice);
			$temp_ht = $fields['totalht'] * $ratiopaymentinvoice;
			$temp_vat = $fields['localtax'.$localTaxType] * $ratiopaymentinvoice;
			$subtot_paye_total_ht += $temp_ht;
			$subtot_paye_vat      += $temp_vat;
			$x_paye_sum           += $temp_vat;
		}
	}
	print '<td class="nowrap right">'.price(price2num($x_paye_sum, 'MT')).'</td>';

	$subtotalcoll = $subtotalcoll + $x_coll_sum;
	$subtotalpaid = $subtotalpaid + $x_paye_sum;

	$diff = $x_coll_sum - $x_paye_sum;
	$total = $total + $diff;
	$subtotal = price2num($subtotal + $diff, 'MT');

	print '<td class="nowrap right">'.price(price2num($diff, 'MT')).'</td>'."\n";
	print "<td>&nbsp;</td>\n";
	print "</tr>\n";

	$i++;
	$m++;
	if ($i > 2) {
		print '<tr class="liste_total">';
		print '<td class="right"><a href="quadri_detail.php?leftmenu=tax_vat&q='.round($m / 3).'&year='.$y.'">'.$langs->trans("SubTotal").'</a>:</td>';
		print '<td class="nowrap right">'.price(price2num($subtotalcoll, 'MT')).'</td>';
		print '<td class="nowrap right">'.price(price2num($subtotalpaid, 'MT')).'</td>';
		print '<td class="nowrap right">'.price(price2num($subtotal, 'MT')).'</td>';
		print '<td>&nbsp;</td></tr>';
		$i = 0;
		$subtotalcoll = 0;
		$subtotalpaid = 0;
		$subtotal = 0;
	}
}
print '<tr class="liste_total"><td class="right" colspan="3">'.$langs->trans("TotalToPay").':</td><td class="nowrap right">'.price(price2num($total, 'MT')).'</td>';
print "<td>&nbsp;</td>\n";
print '</tr>';

print '</table>';


print '</div><div class="fichetwothirdright">';


/*
 * Paid
 */

print load_fiche_titre($langs->transcountry($LTPaid, $mysoc->country_code), '', '');

$sql = '';

$sql .= "SELECT SUM(amount) as mm, date_format(f.datev,'%Y-%m') as dm, 'claimed' as mode";
$sql .= " FROM ".MAIN_DB_PREFIX."localtax as f";
$sql .= " WHERE f.entity = ".$conf->entity;
$sql .= " AND (f.datev >= '".$db->idate($date_start)."' AND f.datev <= '".$db->idate($date_end)."')";
$sql .= " AND localtaxtype=".((int) $localTaxType);
$sql .= " GROUP BY dm";

$sql .= " UNION ";

$sql .= "SELECT SUM(amount) as mm, date_format(f.datep,'%Y-%m') as dm, 'paid' as mode";
$sql .= " FROM ".MAIN_DB_PREFIX."localtax as f";
$sql .= " WHERE f.entity = ".$conf->entity;
$sql .= " AND (f.datep >= '".$db->idate($date_start)."' AND f.datep <= '".$db->idate($date_end)."')";
$sql .= " AND localtaxtype=".((int) $localTaxType);
$sql .= " GROUP BY dm";

$sql .= " ORDER BY dm ASC, mode ASC";
//print $sql;

pt($db, $sql, $langs->trans("Month"));


print '</div></div>';

// End of page
llxFooter();
$db->close();
