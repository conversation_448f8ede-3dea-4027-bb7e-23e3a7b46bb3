<?php
/**
 * Configuration et administration du module Plan Formation avec onglets
 */

require_once('../config.php');
require_once DOL_DOCUMENT_ROOT.'/core/lib/admin.lib.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/html.form.class.php';

// Security check
if (!$user->admin) {
    accessforbidden();
}

$langs->load('planformation@planformation');
$langs->load('admin');

$action = GETPOST('action', 'alpha');
$tab = GETPOST('tab', 'alpha') ?: 'config';

// Actions
if ($action == 'save_config') {
    // Dashboard settings
    dolibarr_set_const($db, 'PLANFORMATION_DASHBOARD_REFRESH', GETPOST('dashboard_refresh', 'int'), 'int', 0, '', $conf->entity);
    dolibarr_set_const($db, 'PLANFORMATION_DASHBOARD_CHARTS', GETPOST('dashboard_charts', 'alpha'), 'chaine', 0, '', $conf->entity);
    
    // Certificate settings
    dolibarr_set_const($db, 'PLANFORMATION_CERT_TEMPLATE', GETPOST('cert_template', 'alpha'), 'chaine', 0, '', $conf->entity);
    dolibarr_set_const($db, 'PLANFORMATION_CERT_SIGNATURE', GETPOST('cert_signature', 'alpha'), 'chaine', 0, '', $conf->entity);
    
    // Integration settings
    dolibarr_set_const($db, 'PLANFORMATION_TICKET_AUTO', GETPOST('ticket_auto', 'alpha'), 'chaine', 0, '', $conf->entity);
    dolibarr_set_const($db, 'PLANFORMATION_SUPPLIER_EVAL', GETPOST('supplier_eval', 'alpha'), 'chaine', 0, '', $conf->entity);
    
    // Print settings
    dolibarr_set_const($db, 'PLANFORMATION_PRINT_LOGO', GETPOST('print_logo', 'alpha'), 'chaine', 0, '', $conf->entity);
    dolibarr_set_const($db, 'PLANFORMATION_PRINT_FOOTER', GETPOST('print_footer', 'alpha'), 'chaine', 0, '', $conf->entity);
    
    setEventMessages($langs->trans('SetupSaved'), null, 'mesgs');
}

$title = $langs->trans('PFSetupAndAdmin');
llxHeader('', $title);

$linkback = '<a href="' . DOL_URL_ROOT . '/admin/modules.php?restore_lastsearch_values=1">' . $langs->trans("BackToModuleList") . '</a>';
print load_fiche_titre($title, $linkback, 'planformation@planformation');

// Préparation des onglets
$head = array();
$h = 0;

$head[$h][0] = $_SERVER['PHP_SELF'] . '?tab=config';
$head[$h][1] = '<i class="fas fa-cogs"></i> Configuration';
$head[$h][2] = 'config';
$h++;

$head[$h][0] = $_SERVER['PHP_SELF'] . '?tab=integrations';
$head[$h][1] = '<i class="fas fa-puzzle-piece"></i> Intégrations';
$head[$h][2] = 'integrations';
$h++;

$head[$h][0] = $_SERVER['PHP_SELF'] . '?tab=tools';
$head[$h][1] = '<i class="fas fa-tools"></i> Outils et Rapports';
$head[$h][2] = 'tools';
$h++;

$head[$h][0] = $_SERVER['PHP_SELF'] . '?tab=tests';
$head[$h][1] = '<i class="fas fa-vial"></i> Tests';
$head[$h][2] = 'tests';
$h++;

$head[$h][0] = $_SERVER['PHP_SELF'] . '?tab=administration';
$head[$h][1] = '<i class="fas fa-cogs"></i> Administration';
$head[$h][2] = 'administration';
$h++;

$head[$h][0] = $_SERVER['PHP_SELF'] . '?tab=about';
$head[$h][1] = '<i class="fas fa-info-circle"></i> À propos';
$head[$h][2] = 'about';
$h++;

$head[$h][0] = $_SERVER['PHP_SELF'] . '?tab=maintenance';
$head[$h][1] = '<i class="fas fa-wrench"></i> Maintenance';
$head[$h][2] = 'maintenance';
$h++;

print dol_get_fiche_head($head, $tab, $langs->trans('PFSetupAndAdmin'), -1, 'planformation@planformation');

// CSS pour améliorer le design
print '<style>
.setup-card {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.07);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.setup-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.1);
}

.setup-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.setup-card-icon {
    font-size: 2em;
    margin-right: 15px;
    color: var(--card-color, #007bff);
}

.setup-card-title {
    font-size: 1.3em;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.setup-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.feature-item {
    display: flex;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 15px;
    transition: background-color 0.2s ease;
}

.feature-item:hover {
    background: #e9ecef;
}

.feature-icon {
    font-size: 1.5em;
    margin-right: 15px;
    width: 40px;
    text-align: center;
}

.feature-content {
    flex: 1;
}

.feature-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.feature-desc {
    font-size: 0.9em;
    color: #6c757d;
    margin: 0;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 600;
    text-transform: uppercase;
}

.status-enabled {
    background: #d4edda;
    color: #155724;
}

.status-disabled {
    background: #f8d7da;
    color: #721c24;
}

@media (max-width: 768px) {
    .setup-grid {
        grid-template-columns: 1fr;
    }
}
</style>';

// Contenu selon l'onglet sélectionné
switch ($tab) {
    case 'config':
        include_once('tabs/config_tab.php');
        break;
    case 'integrations':
        include_once('tabs/integrations_tab.php');
        break;
    case 'tools':
        include_once('tabs/tools_tab.php');
        break;
    case 'tests':
        include_once('tabs/tests_tab.php');
        break;
    case 'administration':
        include_once('tabs/administration_tab.php');
        break;
    case 'about':
        include_once('tabs/about_tab.php');
        break;
    case 'maintenance':
        include_once('tabs/maintenance_tab.php');
        break;
    default:
        include_once('tabs/config_tab.php');
        break;
}

print dol_get_fiche_end();

llxFooter();
?>
