<?php
/**
 * Page d'administration principale du module Plan Formation
 */

// Define top_httphead function early to avoid CSRF check issues
if (!function_exists('top_httphead')) {
    function top_httphead($contenttype = 'text/html', $forcenocache = 0) {
        if ($contenttype == 'text/html') {
            header("Content-Type: text/html; charset=utf-8");
        } else {
            header("Content-Type: ".$contenttype);
        }
        header('X-Frame-Options: SAMEORIGIN');
        header('X-Content-Type-Options: nosniff');
    }
}

// Load Dolibarr environment
$res = 0;
// Try main.inc.php into web root known defined into CONTEXT_DOCUMENT_ROOT (not always defined)
if (!$res && !empty($_SERVER["CONTEXT_DOCUMENT_ROOT"])) {
	$res = @include $_SERVER["CONTEXT_DOCUMENT_ROOT"]."/main.inc.php";
}
// Try main.inc.php into web root detected using web root calculated from SCRIPT_FILENAME
$tmp = empty($_SERVER['SCRIPT_FILENAME']) ? '' : $_SERVER['SCRIPT_FILENAME']; $tmp2 = realpath(__FILE__); $i = strlen($tmp) - 1; $j = strlen($tmp2) - 1;
while ($i > 0 && $j > 0 && isset($tmp[$i]) && isset($tmp2[$j]) && $tmp[$i] == $tmp2[$j]) {
	$i--; $j--;
}
if (!$res && $i > 0 && file_exists(substr($tmp, 0, ($i + 1))."/main.inc.php")) {
	$res = @include substr($tmp, 0, ($i + 1))."/main.inc.php";
}
if (!$res && $i > 0 && file_exists(dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php")) {
	$res = @include dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php";
}
// Try main.inc.php using relative path
if (!$res && file_exists("../../main.inc.php")) {
	$res = @include "../../main.inc.php";
}
if (!$res && file_exists("../../../main.inc.php")) {
	$res = @include "../../../main.inc.php";
}
if (!$res) {
	die("Include of main fails");
}

// Load abricot framework if needed
if(!dol_include_once('/abricot/inc.core.php')) {
	print 'Abricot framework not found. Please install it first.';
	exit;
}
require_once DOL_DOCUMENT_ROOT.'/core/lib/admin.lib.php';

// Security check
if (!$user->admin) {
    accessforbidden();
}

$langs->load('planformation@planformation');
$langs->load('admin');

$title = $langs->trans('PFAdministration');
llxHeader('', $title);

$linkback = '<a href="' . DOL_URL_ROOT . '/admin/modules.php?restore_lastsearch_values=1">' . $langs->trans("BackToModuleList") . '</a>';
print load_fiche_titre($title, $linkback, 'planformation@planformation');

// Section Configuration
print '<div class="div-table-responsive-no-min">';
print '<table class="noborder centpercent">';
print '<tr class="liste_titre">';
print '<th colspan="3"><i class="fas fa-cogs"></i> ' . $langs->trans('Configuration') . '</th>';
print '</tr>';

// Configuration avancée
print '<tr class="oddeven">';
print '<td width="20%"><i class="fas fa-sliders-h fa-2x" style="color: #007bff;"></i></td>';
print '<td>';
print '<strong>' . $langs->trans('PFAdvancedSetup') . '</strong><br>';
print '<span class="opacitymedium">' . $langs->trans('PFAdvancedSetupDesc') . '</span>';
print '</td>';
print '<td class="center" width="20%">';
print '<a class="butAction" href="setup.php">';
print '<i class="fas fa-cogs"></i> ' . $langs->trans('Configure');
print '</a>';
print '</td>';
print '</tr>';

// Permissions
print '<tr class="oddeven">';
print '<td><i class="fas fa-shield-alt fa-2x" style="color: #28a745;"></i></td>';
print '<td>';
print '<strong>' . $langs->trans('Permissions') . '</strong><br>';
print '<span class="opacitymedium">' . $langs->trans('PFPermissionsDesc') . '</span>';
print '</td>';
print '<td class="center">';
print '<a class="butAction" href="' . DOL_URL_ROOT . '/admin/perms.php?module=planformation">';
print '<i class="fas fa-users-cog"></i> ' . $langs->trans('Configure');
print '</a>';
print '</td>';
print '</tr>';

print '</table>';
print '</div>';

print '<br>';

// Section Maintenance
print '<div class="div-table-responsive-no-min">';
print '<table class="noborder centpercent">';
print '<tr class="liste_titre">';
print '<th colspan="3"><i class="fas fa-tools"></i> ' . $langs->trans('Maintenance') . '</th>';
print '</tr>';

// Tests et validation
print '<tr class="oddeven">';
print '<td width="20%"><i class="fas fa-check-circle fa-2x" style="color: #17a2b8;"></i></td>';
print '<td>';
print '<strong>' . $langs->trans('PFTestValidation') . '</strong><br>';
print '<span class="opacitymedium">' . $langs->trans('PFTestValidationDesc') . '</span>';
print '</td>';
print '<td class="center" width="20%">';
print '<a class="butAction" href="../test_module.php">';
print '<i class="fas fa-vial"></i> ' . $langs->trans('RunTests');
print '</a>';
print '</td>';
print '</tr>';

// Mise à jour
print '<tr class="oddeven">';
print '<td><i class="fas fa-sync-alt fa-2x" style="color: #ffc107;"></i></td>';
print '<td>';
print '<strong>' . $langs->trans('PFUpdateModule') . '</strong><br>';
print '<span class="opacitymedium">' . $langs->trans('PFUpdateModuleDesc') . '</span>';
print '</td>';
print '<td class="center">';
print '<a class="butAction" href="../update_module.php">';
print '<i class="fas fa-download"></i> ' . $langs->trans('Update');
print '</a>';
print '</td>';
print '</tr>';

// Sauvegarde
print '<tr class="oddeven">';
print '<td><i class="fas fa-database fa-2x" style="color: #6f42c1;"></i></td>';
print '<td>';
print '<strong>' . $langs->trans('PFBackupData') . '</strong><br>';
print '<span class="opacitymedium">' . $langs->trans('PFBackupDataDesc') . '</span>';
print '</td>';
print '<td class="center">';
print '<a class="butAction" href="backup.php">';
print '<i class="fas fa-save"></i> ' . $langs->trans('Backup');
print '</a>';
print '</td>';
print '</tr>';

print '</table>';
print '</div>';

print '<br>';

// Section Statistiques système
print '<div class="div-table-responsive-no-min">';
print '<table class="noborder centpercent">';
print '<tr class="liste_titre">';
print '<th colspan="2"><i class="fas fa-chart-bar"></i> ' . $langs->trans('PFSystemStats') . '</th>';
print '</tr>';

// Récupération des statistiques système
$system_stats = getSystemStats();

print '<tr class="oddeven">';
print '<td width="50%">' . $langs->trans('PFDatabaseTables') . '</td>';
print '<td><strong>' . $system_stats['tables_count'] . '</strong></td>';
print '</tr>';

print '<tr class="oddeven">';
print '<td>' . $langs->trans('PFTotalRecords') . '</td>';
print '<td><strong>' . number_format($system_stats['total_records']) . '</strong></td>';
print '</tr>';

print '<tr class="oddeven">';
print '<td>' . $langs->trans('PFDatabaseSize') . '</td>';
print '<td><strong>' . $system_stats['database_size'] . '</strong></td>';
print '</tr>';

print '<tr class="oddeven">';
print '<td>' . $langs->trans('PFModuleVersion') . '</td>';
print '<td><strong>2.0</strong></td>';
print '</tr>';

print '<tr class="oddeven">';
print '<td>' . $langs->trans('PFLastUpdate') . '</td>';
print '<td><strong>' . dol_print_date(dol_now(), 'dayhour') . '</strong></td>';
print '</tr>';

print '</table>';
print '</div>';

print '<br>';

// Section Logs et monitoring
print '<div class="div-table-responsive-no-min">';
print '<table class="noborder centpercent">';
print '<tr class="liste_titre">';
print '<th colspan="3"><i class="fas fa-list-alt"></i> ' . $langs->trans('PFLogsMonitoring') . '</th>';
print '</tr>';

// Logs d'activité
print '<tr class="oddeven">';
print '<td width="20%"><i class="fas fa-file-alt fa-2x" style="color: #dc3545;"></i></td>';
print '<td>';
print '<strong>' . $langs->trans('PFActivityLogs') . '</strong><br>';
print '<span class="opacitymedium">' . $langs->trans('PFActivityLogsDesc') . '</span>';
print '</td>';
print '<td class="center" width="20%">';
print '<a class="butAction" href="logs.php">';
print '<i class="fas fa-eye"></i> ' . $langs->trans('View');
print '</a>';
print '</td>';
print '</tr>';

// Monitoring performance
print '<tr class="oddeven">';
print '<td><i class="fas fa-tachometer-alt fa-2x" style="color: #20c997;"></i></td>';
print '<td>';
print '<strong>' . $langs->trans('PFPerformanceMonitoring') . '</strong><br>';
print '<span class="opacitymedium">' . $langs->trans('PFPerformanceMonitoringDesc') . '</span>';
print '</td>';
print '<td class="center">';
print '<a class="butAction" href="performance.php">';
print '<i class="fas fa-chart-line"></i> ' . $langs->trans('Monitor');
print '</a>';
print '</td>';
print '</tr>';

print '</table>';
print '</div>';

print '<br>';

// Liens rapides
print '<div class="center">';
print '<h3>' . $langs->trans('PFQuickLinks') . '</h3>';
print '<a class="butAction" href="../dashboard.php">';
print '<i class="fas fa-tachometer-alt"></i> ' . $langs->trans('PFDashboard');
print '</a> ';
print '<a class="butAction" href="../tools_reports.php">';
print '<i class="fas fa-tools"></i> ' . $langs->trans('PFToolsReports');
print '</a> ';
print '<a class="butAction" href="../integrations.php">';
print '<i class="fas fa-puzzle-piece"></i> ' . $langs->trans('PFIntegrations');
print '</a>';
print '</div>';

llxFooter();

/**
 * Récupère les statistiques système
 */
function getSystemStats() {
    global $db;
    
    $stats = array(
        'tables_count' => 0,
        'total_records' => 0,
        'database_size' => '0 MB'
    );
    
    // Compter les tables du module
    $tables = array(
        'planform',
        'planform_formation', 
        'planform_session',
        'planform_session_participant',
        'planform_session_presence'
    );
    
    $stats['tables_count'] = count($tables);
    
    // Compter les enregistrements
    $total_records = 0;
    foreach ($tables as $table) {
        $sql = "SELECT COUNT(*) as count FROM " . MAIN_DB_PREFIX . $table;
        $resql = $db->query($sql);
        if ($resql && ($obj = $db->fetch_object($resql))) {
            $total_records += $obj->count;
        }
    }
    $stats['total_records'] = $total_records;
    
    // Taille approximative (simulation)
    $stats['database_size'] = round($total_records * 0.5, 1) . ' KB';
    
    return $stats;
}
?>
