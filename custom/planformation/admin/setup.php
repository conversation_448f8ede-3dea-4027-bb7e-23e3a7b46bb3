<?php
/**
 * Configuration avancée du module Plan Formation v2.0
 */

require_once('../config.php');
require_once DOL_DOCUMENT_ROOT.'/core/lib/admin.lib.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/html.form.class.php';

// Security check
if (!$user->admin) {
    accessforbidden();
}

$langs->load('planformation@planformation');
$langs->load('admin');

$action = GETPOST('action', 'alpha');

// Actions
if ($action == 'save') {
    // Dashboard settings
    dolibarr_set_const($db, 'PLANFORMATION_DASHBOARD_REFRESH', GETPOST('dashboard_refresh', 'int'), 'int', 0, '', $conf->entity);
    dolibarr_set_const($db, 'PLANFORMATION_DASHBOARD_CHARTS', GETPOST('dashboard_charts', 'alpha'), 'chaine', 0, '', $conf->entity);
    
    // Certificate settings
    dolibarr_set_const($db, 'PLANFORMATION_CERT_TEMPLATE', GETPOST('cert_template', 'alpha'), 'chaine', 0, '', $conf->entity);
    dolibarr_set_const($db, 'PLANFORMATION_CERT_SIGNATURE', GETPOST('cert_signature', 'alpha'), 'chaine', 0, '', $conf->entity);
    
    // Integration settings
    dolibarr_set_const($db, 'PLANFORMATION_TICKET_AUTO', GETPOST('ticket_auto', 'alpha'), 'chaine', 0, '', $conf->entity);
    dolibarr_set_const($db, 'PLANFORMATION_SUPPLIER_EVAL', GETPOST('supplier_eval', 'alpha'), 'chaine', 0, '', $conf->entity);
    
    // Print settings
    dolibarr_set_const($db, 'PLANFORMATION_PRINT_LOGO', GETPOST('print_logo', 'alpha'), 'chaine', 0, '', $conf->entity);
    dolibarr_set_const($db, 'PLANFORMATION_PRINT_FOOTER', GETPOST('print_footer', 'alpha'), 'chaine', 0, '', $conf->entity);
    
    setEventMessages($langs->trans('SetupSaved'), null, 'mesgs');
}

$title = $langs->trans('PFAdvancedSetup');
llxHeader('', $title);

$linkback = '<a href="' . DOL_URL_ROOT . '/admin/modules.php?restore_lastsearch_values=1">' . $langs->trans("BackToModuleList") . '</a>';
print load_fiche_titre($title, $linkback, 'planformation@planformation');

$head = planformation_admin_prepare_head();
print dol_get_fiche_head($head, 'setup', $langs->trans('PFModule'), -1, 'planformation@planformation');

print '<form method="POST" action="' . $_SERVER['PHP_SELF'] . '">';
print '<input type="hidden" name="token" value="' . newToken() . '">';
print '<input type="hidden" name="action" value="save">';

// Dashboard Configuration
print '<table class="noborder centpercent">';
print '<tr class="liste_titre">';
print '<th colspan="2">' . $langs->trans('PFDashboardConfig') . '</th>';
print '</tr>';

// Refresh interval
print '<tr class="oddeven">';
print '<td width="50%">' . $langs->trans('PFDashboardRefresh') . '</td>';
print '<td>';
print '<select name="dashboard_refresh" class="flat">';
print '<option value="0"' . (getDolGlobalString('PLANFORMATION_DASHBOARD_REFRESH') == '0' ? ' selected' : '') . '>' . $langs->trans('Disabled') . '</option>';
print '<option value="300"' . (getDolGlobalString('PLANFORMATION_DASHBOARD_REFRESH') == '300' ? ' selected' : '') . '>5 ' . $langs->trans('Minutes') . '</option>';
print '<option value="600"' . (getDolGlobalString('PLANFORMATION_DASHBOARD_REFRESH') == '600' ? ' selected' : '') . '>10 ' . $langs->trans('Minutes') . '</option>';
print '<option value="1800"' . (getDolGlobalString('PLANFORMATION_DASHBOARD_REFRESH') == '1800' ? ' selected' : '') . '>30 ' . $langs->trans('Minutes') . '</option>';
print '</select>';
print '<br><span class="opacitymedium">' . $langs->trans('PFDashboardRefreshDesc') . '</span>';
print '</td>';
print '</tr>';

// Charts display
print '<tr class="oddeven">';
print '<td>' . $langs->trans('PFDashboardCharts') . '</td>';
print '<td>';
print '<input type="checkbox" name="dashboard_charts" value="1"' . (getDolGlobalString('PLANFORMATION_DASHBOARD_CHARTS') ? ' checked' : '') . '> ';
print $langs->trans('PFEnableCharts');
print '<br><span class="opacitymedium">' . $langs->trans('PFEnableChartsDesc') . '</span>';
print '</td>';
print '</tr>';

print '</table>';

print '<br>';

// Certificate Configuration
print '<table class="noborder centpercent">';
print '<tr class="liste_titre">';
print '<th colspan="2">' . $langs->trans('PFCertificateConfig') . '</th>';
print '</tr>';

// Template selection
print '<tr class="oddeven">';
print '<td width="50%">' . $langs->trans('PFCertTemplate') . '</td>';
print '<td>';
print '<select name="cert_template" class="flat">';
print '<option value="default"' . (getDolGlobalString('PLANFORMATION_CERT_TEMPLATE') == 'default' ? ' selected' : '') . '>' . $langs->trans('PFTemplateDefault') . '</option>';
print '<option value="modern"' . (getDolGlobalString('PLANFORMATION_CERT_TEMPLATE') == 'modern' ? ' selected' : '') . '>' . $langs->trans('PFTemplateModern') . '</option>';
print '<option value="classic"' . (getDolGlobalString('PLANFORMATION_CERT_TEMPLATE') == 'classic' ? ' selected' : '') . '>' . $langs->trans('PFTemplateClassic') . '</option>';
print '</select>';
print '<br><span class="opacitymedium">' . $langs->trans('PFCertTemplateDesc') . '</span>';
print '</td>';
print '</tr>';

// Electronic signature
print '<tr class="oddeven">';
print '<td>' . $langs->trans('PFCertSignature') . '</td>';
print '<td>';
print '<input type="checkbox" name="cert_signature" value="1"' . (getDolGlobalString('PLANFORMATION_CERT_SIGNATURE') ? ' checked' : '') . '> ';
print $langs->trans('PFEnableElectronicSignature');
print '<br><span class="opacitymedium">' . $langs->trans('PFElectronicSignatureDesc') . '</span>';
print '</td>';
print '</tr>';

print '</table>';

print '<br>';

// Integration Configuration
print '<table class="noborder centpercent">';
print '<tr class="liste_titre">';
print '<th colspan="2">' . $langs->trans('PFIntegrationConfig') . '</th>';
print '</tr>';

// Auto ticket creation
print '<tr class="oddeven">';
print '<td width="50%">' . $langs->trans('PFTicketAuto') . '</td>';
print '<td>';
print '<input type="checkbox" name="ticket_auto" value="1"' . (getDolGlobalString('PLANFORMATION_TICKET_AUTO') ? ' checked' : '') . '> ';
print $langs->trans('PFEnableAutoTicket');
print '<br><span class="opacitymedium">' . $langs->trans('PFAutoTicketDesc') . '</span>';
print '</td>';
print '</tr>';

// Supplier evaluation
print '<tr class="oddeven">';
print '<td>' . $langs->trans('PFSupplierEval') . '</td>';
print '<td>';
print '<input type="checkbox" name="supplier_eval" value="1"' . (getDolGlobalString('PLANFORMATION_SUPPLIER_EVAL') ? ' checked' : '') . '> ';
print $langs->trans('PFEnableSupplierEval');
print '<br><span class="opacitymedium">' . $langs->trans('PFSupplierEvalDesc') . '</span>';
if (!isModEnabled('supplierassessment')) {
    print '<br><span style="color: orange;">' . $langs->trans('PFSupplierAssessmentRequired') . '</span>';
}
print '</td>';
print '</tr>';

print '</table>';

print '<br>';

// Print Configuration
print '<table class="noborder centpercent">';
print '<tr class="liste_titre">';
print '<th colspan="2">' . $langs->trans('PFPrintConfig') . '</th>';
print '</tr>';

// Logo in prints
print '<tr class="oddeven">';
print '<td width="50%">' . $langs->trans('PFPrintLogo') . '</td>';
print '<td>';
print '<input type="checkbox" name="print_logo" value="1"' . (getDolGlobalString('PLANFORMATION_PRINT_LOGO') ? ' checked' : '') . '> ';
print $langs->trans('PFIncludeLogo');
print '<br><span class="opacitymedium">' . $langs->trans('PFIncludeLogoDesc') . '</span>';
print '</td>';
print '</tr>';

// Custom footer
print '<tr class="oddeven">';
print '<td>' . $langs->trans('PFPrintFooter') . '</td>';
print '<td>';
print '<textarea name="print_footer" rows="3" cols="50" class="flat">' . getDolGlobalString('PLANFORMATION_PRINT_FOOTER') . '</textarea>';
print '<br><span class="opacitymedium">' . $langs->trans('PFPrintFooterDesc') . '</span>';
print '</td>';
print '</tr>';

print '</table>';

print '<br>';

print '<div class="center">';
print '<input type="submit" class="button" value="' . $langs->trans('Save') . '">';
print '</div>';

print '</form>';

print dol_get_fiche_end();

llxFooter();

/**
 * Prepare admin pages header
 */
function planformation_admin_prepare_head() {
    global $langs, $conf;

    $langs->load('planformation@planformation');

    $h = 0;
    $head = array();

    $head[$h][0] = DOL_URL_ROOT . '/custom/planformation/admin/setup.php';
    $head[$h][1] = $langs->trans('PFAdvancedSetup');
    $head[$h][2] = 'setup';
    $h++;

    complete_head_from_modules($conf, $langs, null, $head, $h, 'planformation');

    return $head;
}
?>
