<?php

require_once DOL_DOCUMENT_ROOT.'/custom/purchase_request/core/modules/modules_purchase_request.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/company.lib.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/pdf.lib.php';

class pdf_purchase_request extends ModelePDFPurchaseRequest
{
    public $version = 'dolibarr';
    public $page_largeur;
    public $page_hauteur;
    public $format;
    public $marge_gauche;
    public $marge_droite;
    public $marge_haute;
    public $marge_basse;

    public function __construct($db)
    {
        global $conf, $langs, $mysoc;
        
        parent::__construct($db);

        $this->description = "Standard purchase request PDF model";
        $this->name = "purchase_request";

        // Page size
        $this->format = array($this->page_largeur, $this->page_hauteur);
        $this->marge_gauche = isset($conf->global->MAIN_PDF_MARGIN_LEFT) ? $conf->global->MAIN_PDF_MARGIN_LEFT : 10;
        $this->marge_droite = isset($conf->global->MAIN_PDF_MARGIN_RIGHT) ? $conf->global->MAIN_PDF_MARGIN_RIGHT : 10;
        $this->marge_haute = isset($conf->global->MAIN_PDF_MARGIN_TOP) ? $conf->global->MAIN_PDF_MARGIN_TOP : 10;
        $this->marge_basse = isset($conf->global->MAIN_PDF_MARGIN_BOTTOM) ? $conf->global->MAIN_PDF_MARGIN_BOTTOM : 10;
    }

    public function write_file($object, $outputlangs)
    {
        global $user, $langs, $conf, $mysoc, $hookmanager;

        if (!is_object($outputlangs)) $outputlangs = $langs;

        $outputlangs->loadLangs(array("main", "dict", "companies", "bills", "products", "orders", "deliveries"));

        $nblines = count($object->lines);

        // Create PDF
        $pdf = pdf_getInstance($this->format);
        $pdf->SetAutoPageBreak(1, 0);
        $pdf->SetFont('', '', $default_font_size - 1);

        $pdf->Open();
        $pagenb = 0;
        $pdf->SetDrawColor(128, 128, 128);

        $pdf->SetTitle($outputlangs->convToOutputCharset($object->ref));
        $pdf->SetSubject($outputlangs->transnoentities("PurchaseRequest"));
        $pdf->SetCreator("Dolibarr ".DOL_VERSION);
        $pdf->SetAuthor($outputlangs->convToOutputCharset($user->getFullName($outputlangs)));
        $pdf->SetKeyWords($outputlangs->convToOutputCharset($object->ref)." ".$outputlangs->transnoentities("PurchaseRequest"));

        $pdf->AddPage();
        $pagenb++;

        // Output content here...

        // Store PDF in file
        $file = $conf->purchase_request->dir_output . "/" . dol_sanitizeFileName($object->ref) . ".pdf";
        $pdf->Output($file, 'F');

        $this->result = array('fullpath' => $file);

        return 1;   
    }
}