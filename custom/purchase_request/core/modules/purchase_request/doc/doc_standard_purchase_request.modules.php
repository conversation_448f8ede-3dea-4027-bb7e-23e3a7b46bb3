<?php

require_once DOL_DOCUMENT_ROOT.'/core/lib/pdf.lib.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/company.lib.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/functions2.lib.php';
require_once DOL_DOCUMENT_ROOT.'/custom/purchase_request/core/modules/modules_purchase_request.php';
require_once DOL_DOCUMENT_ROOT.'/custom/purchase_request/core/modules/purchase_request/modules_purchase_request.php';

/**
 * Class to generate PDF purchase requests
 */
class doc_standard_purchase_request extends ModelePDFPurchaseRequestBase
{
    public $db;
    public $name;
    public $description;
    public $type;
    public $version = 'dolibarr';
    public $page_largeur;
    public $page_hauteur;
    public $format;

    public function __construct($db)
    {
        global $conf, $langs, $mysoc;
        
        parent::__construct($db);
        
        $this->name = "standard_purchase_request";  // Change this line
        $this->description = $langs->trans('DocumentModelStandardPDF');
        
        $this->type = 'pdf';
        
        // Page dimensions
        $this->page_largeur = 210;
        $this->page_hauteur = 297;
        $this->format = array($this->page_largeur, $this->page_hauteur);
        $this->marge_gauche = getDolGlobalInt('MAIN_PDF_MARGIN_LEFT', 10);
        $this->marge_droite = getDolGlobalInt('MAIN_PDF_MARGIN_RIGHT', 10);
        $this->marge_haute = getDolGlobalInt('MAIN_PDF_MARGIN_TOP', 10);
        $this->marge_basse = getDolGlobalInt('MAIN_PDF_MARGIN_BOTTOM', 10);
    }

    /**
     * Return description of a module
     *
     * @param  Translate $langs Language object
     * @return string          Description
     */
    public function info($langs)
    {
        return $this->description;
    }

    /**
     * Function to generate the PDF document
     *
     * @param  Object    $object  Object to generate
     * @param  Translate $outputlangs Lang output object
     * @return int                    1=OK, 0=KO
     */
    public function write_file($object, $outputlangs)
    {
        global $user, $langs, $conf;
        
        if (!is_object($outputlangs)) $outputlangs = $langs;
        
        // Force output charset to ISO to avoid encoding issues
        if (!empty($conf->global->MAIN_USE_FPDF)) $outputlangs->charset_output = 'ISO-8859-1';
        
        // Load translation files required by page
        $outputlangs->loadLangs(array("main", "dict", "companies", "bills", "products"));
        
        // TODO: Add PDF generation code here
        
        return 1;
    }

    public function getInfo($name='')
    {
        global $langs;
        return $langs->trans("Version").': '.$this->getVersion();
    }

    public function getVersion()
    {
        return $this->version;
    }
}
