<?php

require_once DOL_DOCUMENT_ROOT.'/core/class/commondocgenerator.class.php';

/**
 * Class for PDF models for purchase requests
 */
class ModelePDFPurchaseRequestBase extends CommonDocGenerator
{
    public $db;
    public $name;
    public $description;
    public $type = 'pdf';

    public function __construct($db)
    {
        global $conf, $langs;
        $this->db = $db;
    }

    /**
     * Return list of active models generation
     */
    public static function liste_modeles($db, $maxfilenamelength = 0)
    {
        global $conf;

        $type = 'purchase_request';
        $list = array();
        
        include_once DOL_DOCUMENT_ROOT.'/core/lib/files.lib.php';
        $list = array();
        
        // Add default template
        $list['standard']='Standard';
        
        return $list;
    }

    /**
     * Function to build pdf onto disk
     *
     * @param Object $object Object to generate
     * @param Object $outputlangs Lang output object
     * @return int 1=OK, 0=KO
     */
    public function write_file($object, $outputlangs)
    {
        global $user, $langs, $conf;

        if (! is_object($outputlangs)) $outputlangs = $langs;

        // Define output directory
        $objectref = dol_sanitizeFileName($object->ref);
        $dir = $conf->purchase_request->dir_output;
        $file = $dir . "/" . $objectref . "/" . $objectref . ".pdf";

        if (! file_exists($dir))
        {
            if (dol_mkdir($dir) < 0)
            {
                $this->error = $langs->trans("ErrorCanNotCreateDir", $dir);
                return 0;
            }
        }

        if (file_exists($dir))
        {
            $pdf = pdf_getInstance();
            
            if (class_exists('TCPDF'))
            {
                $pdf->setPrintHeader(false);
                $pdf->setPrintFooter(false);
            }
            $pdf->SetFont('', '', 10);

            $pdf->Open();
            $pdf->AddPage();
            
            // Add content here
            $pdf->SetFont('', 'B', 12);
            $pdf->MultiCell(0, 3, $outputlangs->convToOutputCharset($object->ref), 0, 'L');
            
            // Save file
            $pdf->Close();
            $pdf->Output($file, 'F');

            return 1;
        }
        
        return 0;
    }
}

/**
 * Class for numbering model
 */
class ModeleNumRefPurchaseRequest
{
    /**
     * Return list of numbering models
     *
     * @param   DoliDB  $db     Database handler
     * @return  array           List of models
     */
    public static function liste_modeles($db)
    {
        $liste = array();
        $liste['standard'] = 'Standard';
        return $liste;
    }

    /**
     * Return description of numbering model
     *
     * @return string      Text description
     */
    public function info()
    {
        global $langs;
        return $langs->trans("StandardPurchaseRequestNumberingDesc");
    }
}
